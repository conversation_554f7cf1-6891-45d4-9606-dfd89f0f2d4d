import { Svix, SvixOptions, Webhook } from "svix";
import { logger } from "@repo/logs";
import { config } from "@repo/config";

// Get SVIX token from config or environment variables
const SVIX_TOKEN = process.env.SVIX_TOKEN;
const SVIX_BASE_URL = process.env.SVIX_API_URL || "https://webhooks.cloud.pluggou.io";

// Event type mapping from internal to SVIX format
export const EVENT_TYPE_MAPPING = {
  // Transaction events
  "transaction.created": "Transaction.Created",
  "transaction.updated": "Transaction.Updated",
  "transaction.approved": "Transaction.Approved",
  "transaction.rejected": "Transaction.Rejected",
  "transaction.canceled": "Transaction.Canceled",
  "transaction.processing": "Transaction.Processing",
  "transaction.refunded": "Transaction.Refunded",

  // PIX specific events
  "pix.in.processing": "PixIn.Processing",
  "pix.in.confirmation": "PixIn.Confirmation",
  "pix.out.processing": "PixOut.Processing",
  "pix.out.confirmation": "PixOut.Confirmation",
  "pix.out.failure": "PixOut.Failure",
  "pix.in.reversal.processing": "PixInReversal.Processing",
  "pix.in.reversal.confirmation": "PixInReversal.Confirmation",
  "pix.out.reversal": "PixOut.Reversal",
};

// Reverse mapping from SVIX format to internal
export const SVIX_TO_INTERNAL_EVENT_TYPE = Object.entries(EVENT_TYPE_MAPPING).reduce(
  (acc, [internal, svix]) => {
    acc[svix] = internal;
    return acc;
  },
  {} as Record<string, string>
);

/**
 * SVIX Service Class - Consolidated SVIX functionality
 */
export class SvixService {
  private svix: Svix | null = null;

  constructor() {
    if (SVIX_TOKEN) {
      this.svix = new Svix(SVIX_TOKEN, {
        serverUrl: SVIX_BASE_URL,
      });
      logger.info("SVIX client initialized", {
        serverUrl: SVIX_BASE_URL,
        tokenLength: SVIX_TOKEN.length
      });
    } else {
      logger.warn("SVIX_TOKEN not provided, SVIX functionality will be disabled");
    }
  }

  private ensureSvixClient(): Svix {
    if (!this.svix) {
      throw new Error("SVIX client not initialized. Please provide SVIX_TOKEN environment variable.");
    }
    return this.svix;
  }

  // Create or get a SVIX app for an organization
  async getOrCreateAppForOrganization(organizationId: string, organizationName: string): Promise<string> {
    try {
      const svix = this.ensureSvixClient();

      // Try to find existing app by UID
      const appUid = `org_${organizationId}`;

      try {
        const existingApp = await svix.application.get(appUid);
        return existingApp.id;
      } catch (error) {
        // App doesn't exist, create it
        logger.info("Creating new SVIX app for organization", { organizationId, organizationName });
      }

      // Create new app in SVIX
      const app = await svix.application.create({
        name: `${organizationName} (${organizationId})`,
        uid: appUid,
        metadata: {
          organizationId,
          organizationName
        }
      });

      logger.info("Created SVIX app for organization", { organizationId, appId: app.id });
      return app.id;
    } catch (error) {
      logger.error("Error in getOrCreateAppForOrganization:", error);
      throw error;
    }
  }

  // Create a SVIX endpoint with organization-specific channel
  async createEndpoint(
    appId: string,
    url: string,
    description: string,
    events: string[],
    organizationId?: string
  ): Promise<string> {
    try {
      const svix = this.ensureSvixClient();

      // Create organization-specific channels if organizationId is provided
      const channels = organizationId ? [`org-${organizationId}`] : undefined;

      logger.info("Creating SVIX endpoint", {
        appId,
        url,
        description,
        events,
        organizationId,
        channels,
        serverUrl: SVIX_BASE_URL,
        tokenPresent: !!SVIX_TOKEN,
        tokenLength: SVIX_TOKEN?.length
      });

      const endpoint = await svix.endpoint.create(appId, {
        url,
        description,
        version: 1,
        rateLimit: 100,
        disabled: false,
        filterTypes: events,
        channels
      });

      logger.info("Created SVIX endpoint successfully", {
        appId,
        endpointId: endpoint.id,
        url,
        organizationId,
        channels
      });
      return endpoint.id;
    } catch (error) {
      // Enhanced error logging for debugging
      const errorInfo = {
        message: error instanceof Error ? error.message : String(error || "Unknown error"),
        name: error instanceof Error ? error.name : "UnknownError",
        stack: error instanceof Error ? error.stack : undefined,
        appId,
        url,
        description,
        events,
        organizationId,
        serverUrl: SVIX_BASE_URL,
        tokenPresent: !!SVIX_TOKEN,
        tokenLength: SVIX_TOKEN?.length
      };

      logger.error("Error creating SVIX endpoint:", errorInfo);

      // Check if it's an authentication error
      if (error instanceof Error && (error.message.includes('401') || error.message.includes('Unauthorized'))) {
        logger.error("SVIX Authentication Error - Check token and server URL", {
          serverUrl: SVIX_BASE_URL,
          tokenPresent: !!SVIX_TOKEN,
          tokenLength: SVIX_TOKEN?.length,
          tokenPrefix: SVIX_TOKEN?.substring(0, 20) + '...'
        });
      }

      throw error;
    }
  }

  // Update a SVIX endpoint
  async updateEndpoint(appId: string, endpointId: string, data: {
    url?: string;
    description?: string;
    disabled?: boolean;
    events?: string[]
  }): Promise<void> {
    try {
      const svix = this.ensureSvixClient();

      const updateData: any = {};
      if (data.url) updateData.url = data.url;
      if (data.description) updateData.description = data.description;
      if (data.disabled !== undefined) updateData.disabled = data.disabled;
      if (data.events) updateData.filterTypes = data.events;

      await svix.endpoint.update(appId, endpointId, updateData);
      logger.info("Updated SVIX endpoint", { appId, endpointId });
    } catch (error) {
      logger.error("Error updating SVIX endpoint:", error);
      throw error;
    }
  }

  // Delete a SVIX endpoint
  async deleteEndpoint(appId: string, endpointId: string): Promise<void> {
    try {
      const svix = this.ensureSvixClient();

      await svix.endpoint.delete(appId, endpointId);
      logger.info("Deleted SVIX endpoint", { appId, endpointId });
    } catch (error) {
      logger.error("Error deleting SVIX endpoint:", error);
      throw error;
    }
  }

  // Get endpoint secret
  async getEndpointSecret(appId: string, endpointId: string): Promise<string> {
    try {
      const svix = this.ensureSvixClient();

      const secret = await svix.endpoint.getSecret(appId, endpointId);
      return secret.key || '';
    } catch (error) {
      logger.error("Error getting endpoint secret:", error);
      throw error;
    }
  }

  // Rotate endpoint secret
  async rotateEndpointSecret(appId: string, endpointId: string): Promise<string> {
    try {
      const svix = this.ensureSvixClient();

      await svix.endpoint.rotateSecret(appId, endpointId, {});

      // After rotating, get the new secret
      const secret = await svix.endpoint.getSecret(appId, endpointId);
      return secret.key || '';
    } catch (error) {
      logger.error("Error rotating endpoint secret:", error);
      throw error;
    }
  }

  // Send an event with organization-specific channel
  async sendEvent(appId: string, eventType: string, payload: any, organizationId?: string): Promise<string> {
    try {
      const svix = this.ensureSvixClient();

      // Create organization-specific channel if organizationId is provided
      const channels = organizationId ? [`org-${organizationId}`] : undefined;

      const msg = await svix.message.create(appId, {
        eventType,
        payload,
        channels
      });

      logger.info("Sent event to SVIX", {
        appId,
        eventType,
        messageId: msg.id,
        organizationId,
        channels
      });
      return msg.id;
    } catch (error) {
      // Safely handle error logging
      const errorInfo = {
        message: error instanceof Error ? error.message : String(error || "Unknown SVIX error"),
        name: error instanceof Error ? error.name : "UnknownSvixError",
        stack: error instanceof Error ? error.stack : undefined
      };

      logger.error("Error sending event to SVIX:", errorInfo);
      throw error;
    }
  }

  // Organization management functions
  async createOrganizationChannel(organizationId: string): Promise<void> {
    try {
      // Ensure SVIX client is available
      this.ensureSvixClient();
      const channelName = `org-${organizationId}`;

      // Note: SVIX doesn't have a direct "create channel" API
      // Channels are created implicitly when endpoints are created with channel filters
      // or when messages are sent to specific channels

      logger.info("Organization channel will be created implicitly", {
        organizationId,
        channelName
      });
    } catch (error) {
      // Safely handle error logging
      const errorInfo = {
        message: error instanceof Error ? error.message : String(error || "Unknown error"),
        name: error instanceof Error ? error.name : "UnknownError",
        stack: error instanceof Error ? error.stack : undefined
      };

      logger.error("Error creating organization channel:", errorInfo);
      throw error;
    }
  }

  async disableOrganizationEndpoints(organizationId: string, appId: string): Promise<void> {
    try {
      const svix = this.ensureSvixClient();
      const channelName = `org-${organizationId}`;

      // Get all endpoints for the app
      const endpoints = await svix.endpoint.list(appId);

      // Filter endpoints that belong to this organization (by channel)
      for (const endpoint of endpoints.data) {
        if (endpoint.channels?.includes(channelName)) {
          await svix.endpoint.update(appId, endpoint.id, {
            url: endpoint.url,
            disabled: true
          });
          logger.info("Disabled endpoint for organization", {
            organizationId,
            endpointId: endpoint.id
          });
        }
      }
    } catch (error) {
      // Safely handle error logging
      const errorInfo = {
        message: error instanceof Error ? error.message : String(error || "Unknown error"),
        name: error instanceof Error ? error.name : "UnknownError",
        stack: error instanceof Error ? error.stack : undefined
      };

      logger.error("Error disabling organization endpoints:", errorInfo);
      throw error;
    }
  }

  async enableOrganizationEndpoints(organizationId: string, appId: string): Promise<void> {
    try {
      const svix = this.ensureSvixClient();
      const channelName = `org-${organizationId}`;

      // Get all endpoints for the app
      const endpoints = await svix.endpoint.list(appId);

      // Filter endpoints that belong to this organization (by channel)
      for (const endpoint of endpoints.data) {
        if (endpoint.channels?.includes(channelName)) {
          await svix.endpoint.update(appId, endpoint.id, {
            url: endpoint.url,
            disabled: false
          });
          logger.info("Enabled endpoint for organization", {
            organizationId,
            endpointId: endpoint.id
          });
        }
      }
    } catch (error) {
      // Safely handle error logging
      const errorInfo = {
        message: error instanceof Error ? error.message : String(error || "Unknown error"),
        name: error instanceof Error ? error.name : "UnknownError",
        stack: error instanceof Error ? error.stack : undefined
      };

      logger.error("Error enabling organization endpoints:", errorInfo);
      throw error;
    }
  }
}

// Export singleton instance
export const svixService = new SvixService();

/**
 * Creates a SVIX client instance
 */
export function createSvixClient(options?: SvixOptions): Svix | null {
  if (!SVIX_TOKEN) {
    logger.warn("SVIX_TOKEN not set, SVIX integration disabled");
    return null;
  }

  try {
    return new Svix(SVIX_TOKEN, {
      serverUrl: SVIX_BASE_URL,
      ...options,
    });
  } catch (error) {
    // Safely handle error logging
    const errorInfo = {
      message: error instanceof Error ? error.message : String(error || "Unknown error"),
      name: error instanceof Error ? error.name : "UnknownError",
      stack: error instanceof Error ? error.stack : undefined
    };

    logger.error("Failed to create SVIX client", errorInfo);
    return null;
  }
}

/**
 * Creates an endpoint in SVIX
 */
export async function createSvixEndpoint({
  appId,
  url,
  description,
  version = 1,
  rateLimit = 1000,
  uid,
  secret,
  filterTypes,
  channels = ["flow2pay-events"],
  metadata = {},
  disabled = false,
}: {
  appId: string;
  url: string;
  description?: string;
  version?: number;
  rateLimit?: number;
  uid?: string;
  secret?: string;
  filterTypes?: string[];
  channels?: string[];
  metadata?: Record<string, string>;
  disabled?: boolean;
}) {
  const svix = createSvixClient();
  if (!svix) {
    throw new Error("SVIX client not available");
  }

  try {
    const endpoint = await svix.endpoint.create(appId, {
      url,
      description,
      version,
      rateLimit,
      uid,
      secret,
      filterTypes,
      channels,
      metadata,
      disabled,
    });

    logger.info("Created SVIX endpoint", {
      appId,
      endpointId: endpoint.id,
      url,
    });

    return endpoint;
  } catch (error) {
    // Safely handle error logging
    const errorInfo = {
      message: error instanceof Error ? error.message : String(error || "Unknown error"),
      name: error instanceof Error ? error.name : "UnknownError",
      stack: error instanceof Error ? error.stack : undefined,
      appId,
      url
    };

    logger.error("Failed to create SVIX endpoint", errorInfo);
    throw error;
  }
}

/**
 * Updates an endpoint in SVIX
 */
export async function updateSvixEndpoint({
  appId,
  endpointId,
  url,
  description,
  version,
  rateLimit,
  filterTypes,
  channels,
  metadata,
  disabled,
}: {
  appId: string;
  endpointId: string;
  url?: string;
  description?: string;
  version?: number;
  rateLimit?: number;
  filterTypes?: string[];
  channels?: string[];
  metadata?: Record<string, string>;
  disabled?: boolean;
}) {
  const svix = createSvixClient();
  if (!svix) {
    throw new Error("SVIX client not available");
  }

  try {
    const updateData: any = {};
    if (url !== undefined) updateData.url = url;
    if (description !== undefined) updateData.description = description;
    if (version !== undefined) updateData.version = version;
    if (rateLimit !== undefined) updateData.rateLimit = rateLimit;
    if (filterTypes !== undefined) updateData.filterTypes = filterTypes;
    if (channels !== undefined) updateData.channels = channels;
    if (metadata !== undefined) updateData.metadata = metadata;
    if (disabled !== undefined) updateData.disabled = disabled;

    const endpoint = await svix.endpoint.update(appId, endpointId, updateData);

    logger.info("Updated SVIX endpoint", {
      appId,
      endpointId,
    });

    return endpoint;
  } catch (error) {
    // Safely handle error logging
    const errorInfo = {
      message: error instanceof Error ? error.message : String(error || "Unknown error"),
      name: error instanceof Error ? error.name : "UnknownError",
      stack: error instanceof Error ? error.stack : undefined,
      appId,
      endpointId
    };

    logger.error("Failed to update SVIX endpoint", errorInfo);
    throw error;
  }
}

/**
 * Sends an event to SVIX
 */
export async function sendEventToSvix({
  appId,
  eventType,
  eventData,
  channels = ["flow2pay-events"],
  payload,
  eventId,
}: {
  appId: string;
  eventType: string;
  eventData: any;
  channels?: string[];
  payload?: any;
  eventId?: string;
}) {
  const svix = createSvixClient();
  if (!svix) {
    throw new Error("SVIX client not available");
  }

  // Map internal event type to SVIX format
  const svixEventType = EVENT_TYPE_MAPPING[eventType as keyof typeof EVENT_TYPE_MAPPING] || eventType;

  try {
    const message = await svix.message.create(appId, {
      eventType: svixEventType,
      payload: payload || {
        event_type: eventType,
        svix_event_type: svixEventType,
        ...eventData,
      },
      channels,
      eventId,
    });

    logger.info("Sent event to SVIX", {
      appId,
      messageId: message.id,
      eventType: svixEventType,
    });

    return message;
  } catch (error) {
    // Safely handle error logging
    const errorInfo = {
      message: error instanceof Error ? error.message : String(error || "Unknown error"),
      name: error instanceof Error ? error.name : "UnknownError",
      stack: error instanceof Error ? error.stack : undefined,
      appId,
      eventType: svixEventType
    };

    logger.error("Failed to send event to SVIX", errorInfo);
    throw error;
  }
}

/**
 * Verifies a SVIX webhook signature
 */
export function verifySvixWebhookSignature(
  payload: string,
  headers: Record<string, string>,
  secret: string
): boolean {
  const svix = createSvixClient();
  if (!svix) {
    throw new Error("SVIX client not available");
  }

  try {
    const signature = headers["svix-signature"];
    const timestamp = headers["svix-timestamp"];
    const id = headers["svix-id"];

    if (!signature || !timestamp || !id) {
      logger.warn("Missing SVIX signature headers");
      return false;
    }

    // Verify the signature using the Webhook class
    const wh = new Webhook(secret);
    wh.verify(payload, {
      "svix-id": id,
      "svix-timestamp": timestamp,
      "svix-signature": signature,
    });

    return true;
  } catch (error) {
    // Safely handle error logging
    const errorInfo = {
      message: error instanceof Error ? error.message : String(error || "Unknown error"),
      name: error instanceof Error ? error.name : "UnknownError",
      stack: error instanceof Error ? error.stack : undefined
    };

    logger.error("Failed to verify SVIX webhook signature", errorInfo);
    return false;
  }
}
