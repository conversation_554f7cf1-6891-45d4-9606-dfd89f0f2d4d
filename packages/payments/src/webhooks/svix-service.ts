import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { svixService, EVENT_TYPE_MAPPING } from "@repo/utils";
import { Transaction } from "@prisma/client";

// Default SVIX App ID from environment
const DEFAULT_SVIX_APP_ID = process.env.SVIX_APP_ID || "app_2xJGtEh9B3sOptpW4thRObvtlh9";

/**
 * Gets or creates a SVIX configuration
 */
export async function getSvixConfig() {
  try {
    // Try to get existing config
    let config = await db.svixConfig.findFirst({
      where: { enabled: true },
      orderBy: { updatedAt: "desc" },
    });

    // If no config exists, create one
    if (!config) {
      config = await db.svixConfig.create({
        data: {
          appId: DEFAULT_SVIX_APP_ID,
          appName: "Pluggou PIX",
          enabled: true,
        },
      });
      logger.info("Created SVIX config", { appId: config.appId });
    }

    return config;
  } catch (error) {
    // Safely handle error logging
    const errorInfo = {
      message: error instanceof Error ? error.message : String(error || "Unknown error"),
      name: error instanceof Error ? error.name : "UnknownError",
      stack: error instanceof Error ? error.stack : undefined
    };

    logger.error("Failed to get or create SVIX config", errorInfo);
    throw error;
  }
}

/**
 * Creates a webhook endpoint in SVIX
 */
export async function createSvixWebhookEndpoint(webhook: any) {
  try {
    // Validate webhook object
    if (!webhook || !webhook.id || !webhook.url || !webhook.organizationId) {
      logger.error("Invalid webhook object provided to createSvixWebhookEndpoint", { webhook });
      throw new Error("Invalid webhook object: missing required fields");
    }

    // Ensure events is an array
    if (!webhook.events || !Array.isArray(webhook.events)) {
      logger.warn("Webhook has no events or events is not an array", { webhookId: webhook.id });
      webhook.events = [];
    }

    // Ensure secret exists
    if (!webhook.secret) {
      logger.warn("Webhook has no secret, generating one", { webhookId: webhook.id });
      webhook.secret = `whsec_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;

      // Update the webhook with the new secret
      await db.webhook.update({
        where: { id: webhook.id },
        data: { secret: webhook.secret },
      });
    }

    const config = await getSvixConfig();
    logger.info("Got SVIX config", {
      appId: config.appId,
      webhookId: webhook.id,
      webhookUrl: webhook.url
    });

    // Map event types
    const filterTypes = webhook.events.map((event: string) => {
      const mappedType = EVENT_TYPE_MAPPING[event] || event;
      return mappedType;
    });

    logger.info("Creating SVIX endpoint", {
      webhookId: webhook.id,
      url: webhook.url,
      eventsCount: webhook.events.length,
      filterTypesCount: filterTypes.length,
      filterTypes
    });

    // Create endpoint in SVIX with organization-specific channel
    const endpointId = await svixService.createEndpoint(
      config.appId,
      webhook.url,
      `Webhook for organization ${webhook.organizationId}`,
      filterTypes,
      webhook.organizationId
    );

    const endpoint = {
      id: endpointId,
      url: webhook.url,
      description: `Webhook for organization ${webhook.organizationId}`,
      filterTypes,
      disabled: false,
    };

    if (!endpoint) {
      throw new Error("Failed to create SVIX endpoint: null response");
    }

    logger.info("Successfully created SVIX endpoint, updating webhook", {
      webhookId: webhook.id,
      endpointId: endpoint.id,
    });

    // Update webhook with SVIX endpoint ID
    await db.webhook.update({
      where: { id: webhook.id },
      data: {
        svixEndpointId: endpoint.id,
        useSvix: true,
      },
    });

    logger.info("Created SVIX webhook endpoint", {
      webhookId: webhook.id,
      endpointId: endpoint.id,
    });

    return endpoint;
  } catch (error) {
    logger.error("Failed to create SVIX webhook endpoint", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      webhookId: webhook?.id || 'unknown',
      webhookUrl: webhook?.url || 'unknown',
    });

    // Continue without SVIX integration if it fails
    if (webhook && webhook.id) {
      try {
        await db.webhook.update({
          where: { id: webhook.id },
          data: {
            useSvix: false,
            svixEndpointId: null,
          },
        });
        logger.info("Disabled SVIX integration for webhook due to error", { webhookId: webhook.id });
      } catch (updateError) {
        logger.error("Failed to update webhook after SVIX error", {
          webhookId: webhook.id,
          error: updateError
        });
      }
    }

    throw error;
  }
}

/**
 * Updates a webhook endpoint in SVIX
 */
export async function updateSvixWebhookEndpoint(webhook: any) {
  try {
    if (!webhook.svixEndpointId) {
      logger.warn("Webhook has no SVIX endpoint ID", { webhookId: webhook.id });
      return await createSvixWebhookEndpoint(webhook);
    }

    const config = await getSvixConfig();

    // Update endpoint in SVIX
    await svixService.updateEndpoint(
      config.appId,
      webhook.svixEndpointId,
      {
        url: webhook.url,
        description: `Webhook for organization ${webhook.organizationId}`,
        events: webhook.events.map((event: string) => EVENT_TYPE_MAPPING[event as keyof typeof EVENT_TYPE_MAPPING] || event),
        disabled: !webhook.isActive,
      }
    );

    logger.info("Updated SVIX webhook endpoint", {
      webhookId: webhook.id,
      endpointId: webhook.svixEndpointId,
    });

    return {
      id: webhook.svixEndpointId,
      url: webhook.url,
      description: `Webhook for organization ${webhook.organizationId}`,
      disabled: !webhook.isActive,
    };
  } catch (error) {
    logger.error("Failed to update SVIX webhook endpoint", {
      error,
      webhookId: webhook.id,
    });
    throw error;
  }
}

/**
 * Sends a transaction event to SVIX
 */
export async function sendTransactionEventToSvix(
  transaction: Transaction,
  eventType: string,
  previousStatus?: string
) {
  try {
    const config = await getSvixConfig();

    // Prepare event data
    const eventData = {
      id: transaction.id,
      externalId: transaction.externalId,
      referenceCode: transaction.referenceCode,
      endToEndId: transaction.endToEndId,
      amount: transaction.amount,
      status: transaction.status,
      type: transaction.type,
      customerName: transaction.customerName,
      customerEmail: transaction.customerEmail,
      customerDocument: transaction.customerDocument,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
      paymentAt: transaction.paymentAt,
      organizationId: transaction.organizationId,
    };

    // Send event to SVIX with organization-specific channel
    const messageId = await svixService.sendEvent(
      config.appId,
      eventType,
      eventData,
      transaction.organizationId
    );

    const message = { id: messageId };

    // Create webhook event in database
    const webhookEvent = await db.webhookEvent.create({
      data: {
        type: eventType,
        payload: eventData,
        transactionId: transaction.id,
        organizationId: transaction.organizationId,
        svixMessageId: message.id,
        svixEventType: EVENT_TYPE_MAPPING[eventType as keyof typeof EVENT_TYPE_MAPPING] || eventType,
      },
    });

    logger.info("Sent transaction event to SVIX", {
      transactionId: transaction.id,
      eventType,
      messageId: message.id,
      webhookEventId: webhookEvent.id,
    });

    return { message, webhookEvent };
  } catch (error) {
    // Safely handle error logging
    const errorInfo = {
      message: error instanceof Error ? error.message : String(error || "Unknown error"),
      name: error instanceof Error ? error.name : "UnknownError",
      stack: error instanceof Error ? error.stack : undefined,
      transactionId: transaction.id,
      eventType
    };

    logger.error("Failed to send transaction event to SVIX", errorInfo);
    throw error;
  }
}

/**
 * Migrates existing webhooks to SVIX
 */
export async function migrateWebhooksToSvix() {
  try {
    // Get all webhooks that don't have a SVIX endpoint ID
    const webhooks = await db.webhook.findMany({
      where: {
        svixEndpointId: null,
        isActive: true,
      },
    });

    logger.info(`Migrating ${webhooks.length} webhooks to SVIX`);

    // Create SVIX endpoints for each webhook
    for (const webhook of webhooks) {
      try {
        await createSvixWebhookEndpoint(webhook);
      } catch (error) {
        logger.error("Failed to migrate webhook to SVIX", {
          error,
          webhookId: webhook.id,
        });
      }
    }

    logger.info("Completed webhook migration to SVIX");
  } catch (error) {
    logger.error("Failed to migrate webhooks to SVIX", { error });
    throw error;
  }
}
