/**
 * Serviço de processamento de taxas para transações
 *
 * Este módulo centraliza a lógica de cálculo e aplicação de taxas para transações.
 *
 * Atualização (06/05/2025): Adicionada funcionalidade para armazenar as informações
 * de taxas nos metadados da transação, permitindo que essas informações sejam
 * facilmente acessadas posteriormente.
 */

import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { Transaction, TransactionType } from "@prisma/client";
import { calculateTransactionFees, TransactionFeeType, TransactionFees } from "./calculator";
import { BalanceOperationType, updateOrganizationBalance } from "../balance/balance-service";

/**
 * Processa as taxas para uma transação e atualiza o saldo da organização
 * Esta função centraliza a lógica de cálculo e aplicação de taxas para transações
 *
 * @param transaction A transação a ser processada
 * @param type O tipo de taxa a ser calculada (CHARGE ou TRANSFER)
 * @returns Objeto com informações sobre as taxas aplicadas e o valor líquido
 */
export async function processTransactionFees(
  transaction: Transaction,
  type: TransactionFeeType
): Promise<{
  fees: TransactionFees;
  netAmount: number;
  success: boolean;
}> {
  try {
    logger.info("Processando taxas para transação", {
      transactionId: transaction.id,
      organizationId: transaction.organizationId,
      amount: transaction.amount,
      type
    });

    // Calcular taxas usando a função centralizada
    const fees = await calculateTransactionFees(
      transaction.organizationId,
      transaction.amount,
      type,
      transaction.gatewayId || undefined
    );

    // Calcular valor líquido (valor da transação menos taxas)
    const netAmount = transaction.amount - fees.totalFee;

    logger.info("Taxas calculadas para transação", {
      transactionId: transaction.id,
      grossAmount: transaction.amount,
      fees: fees.totalFee,
      feeSource: fees.source || 'unknown',
      netAmount
    });

    // Verificar se o valor líquido é positivo
    if (netAmount <= 0) {
      logger.warn("Valor líquido negativo ou zero após taxas, não será creditado", {
        transactionId: transaction.id,
        grossAmount: transaction.amount,
        fees: fees.totalFee,
        netAmount
      });
      return { fees, netAmount, success: false };
    }

    return { fees, netAmount, success: true };
  } catch (error) {
    logger.error("Erro ao processar taxas para transação", {
      error,
      transactionId: transaction.id,
      organizationId: transaction.organizationId,
      type
    });
    throw error;
  }
}

/**
 * Processa as taxas para uma transação aprovada e atualiza o saldo da organização
 * Esta função deve ser chamada quando uma transação é aprovada
 *
 * @param transaction A transação aprovada
 * @returns Objeto com informações sobre as taxas aplicadas e o valor líquido
 */
export async function processApprovedTransactionFees(
  transaction: Transaction
): Promise<{
  fees: TransactionFees;
  netAmount: number;
  success: boolean;
}> {
  try {
    // Verificar se a transação é do tipo CHARGE (recebimento)
    if (transaction.type !== TransactionType.CHARGE) {
      logger.info("Ignorando processamento de taxas para transação não-CHARGE", {
        transactionId: transaction.id,
        type: transaction.type
      });
      return {
        fees: { percentFee: 0, fixedFee: 0, totalFee: 0 },
        netAmount: 0,
        success: false
      };
    }

    // Processar taxas
    const result = await processTransactionFees(transaction, 'CHARGE');

    // Se o processamento foi bem-sucedido, atualizar o saldo da organização e os metadados da transação
    if (result.success) {
      // Atualizar o saldo da organização
      await updateOrganizationBalance(
        transaction.organizationId,
        result.netAmount,
        BalanceOperationType.CREDIT,
        transaction.id,
        `Crédito por pagamento aprovado: ${transaction.id} (valor bruto: ${transaction.amount}, taxas: ${result.fees.totalFee})`
      );

      // Recuperar os metadados atuais da transação
      const currentMetadata = transaction.metadata as any || {};

      // Atualizar os metadados da transação E os campos dedicados de taxas
      await db.transaction.update({
        where: { id: transaction.id },
        data: {
          // Update dedicated fee fields for webhook events and UI
          percentFee: result.fees.percentFee,
          fixedFee: result.fees.fixedFee,
          totalFee: result.fees.totalFee,
          netAmount: result.netAmount,
          // Also update metadata for backward compatibility
          metadata: {
            ...currentMetadata,
            fees: {
              percentFee: result.fees.percentFee,
              fixedFee: result.fees.fixedFee,
              totalFee: result.fees.totalFee,
              source: result.fees.source || 'organization',
              calculatedAt: new Date().toISOString()
            },
            netAmount: result.netAmount,
            feeProcessed: true,
            feeProcessedAt: new Date().toISOString()
          }
        }
      });

      logger.info("Saldo e metadados atualizados com sucesso para transação aprovada", {
        transactionId: transaction.id,
        organizationId: transaction.organizationId,
        creditedAmount: result.netAmount,
        fees: result.fees
      });
    }

    return result;
  } catch (error) {
    logger.error("Erro ao processar taxas para transação aprovada", {
      error,
      transactionId: transaction.id,
      organizationId: transaction.organizationId
    });
    throw error;
  }
}

/**
 * Processa as taxas para uma transferência e atualiza o saldo da organização
 * Esta função deve ser chamada quando uma transferência é criada
 *
 * @param transaction A transação de transferência
 * @returns Objeto com informações sobre as taxas aplicadas e o valor total (incluindo taxas)
 */
export async function processTransferFees(
  transaction: Transaction
): Promise<{
  fees: TransactionFees;
  totalAmount: number; // Valor total incluindo taxas
  success: boolean;
}> {
  try {
    // Verificar se a transação é do tipo SEND (transferência)
    if (transaction.type !== TransactionType.SEND) {
      logger.info("Ignorando processamento de taxas para transação não-SEND", {
        transactionId: transaction.id,
        type: transaction.type
      });
      return {
        fees: { percentFee: 0, fixedFee: 0, totalFee: 0 },
        totalAmount: transaction.amount,
        success: false
      };
    }

    // Processar taxas
    const result = await processTransactionFees(transaction, 'TRANSFER');

    // Calcular valor total (valor da transação + taxas)
    const totalAmount = transaction.amount + result.fees.totalFee;

    // Recuperar os metadados atuais da transação
    const currentMetadata = transaction.metadata as any || {};

    // Atualizar os metadados da transação E os campos dedicados de taxas
    await db.transaction.update({
      where: { id: transaction.id },
      data: {
        // Update dedicated fee fields for webhook events and UI
        percentFee: result.fees.percentFee,
        fixedFee: result.fees.fixedFee,
        totalFee: result.fees.totalFee,
        netAmount: transaction.amount, // For transfers, netAmount is the transfer amount (before fees)
        // Also update metadata for backward compatibility
        metadata: {
          ...currentMetadata,
          fees: {
            percentFee: result.fees.percentFee,
            fixedFee: result.fees.fixedFee,
            totalFee: result.fees.totalFee,
            source: result.fees.source || 'organization',
            calculatedAt: new Date().toISOString()
          },
          totalAmount: totalAmount,
          feeProcessed: true,
          feeProcessedAt: new Date().toISOString()
        }
      }
    });

    logger.info("Taxas calculadas e metadados atualizados para transferência", {
      transactionId: transaction.id,
      amount: transaction.amount,
      fees: result.fees.totalFee,
      totalAmount
    });

    return {
      fees: result.fees,
      totalAmount,
      success: true
    };
  } catch (error) {
    logger.error("Erro ao processar taxas para transferência", {
      error,
      transactionId: transaction.id,
      organizationId: transaction.organizationId
    });
    throw error;
  }
}
