import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { Prisma, Transaction, TransactionStatus, TransactionType } from "@prisma/client";
import { calculateTransactionFees } from "../taxes/calculator";

/**
 * Balance operation types
 */
export enum BalanceOperationType {
  CREDIT = "CREDIT",           // Add to available balance
  DEBIT = "DEBIT",             // Remove from available balance
  PENDING = "PENDING",         // Add to pending balance
  RELEASE = "RELEASE",         // Move from pending to available
  RESERVE = "RESERVE",         // Move from available to reserved
  UNRESERVE = "UNRESERVE",     // Move from reserved to available
  DEBIT_RESERVED = "DEBIT_RESERVED", // Remove from reserved balance (for completed transfers)
}

/**
 * Get the current balance for an organization
 * @param organizationId Organization ID
 * @returns The current balance or null if not found
 */
export async function getOrganizationBalance(organizationId: string) {
  try {
    // Get the balance from the database
    const balance = await db.organizationBalance.findUnique({
      where: { organizationId }
    });

    // If no balance record exists, create one with zero values
    if (!balance) {
      return await db.organizationBalance.create({
        data: {
          organizationId,
          availableBalance: 0,
          pendingBalance: 0,
          reservedBalance: 0,
        }
      });
    }

    return balance;
  } catch (error) {
    logger.error("Error getting organization balance", { error, organizationId });
    throw error;
  }
}

/**
 * Check if an organization has sufficient available balance
 * @param organizationId Organization ID
 * @param amount Amount needed
 * @returns Boolean indicating if there's sufficient balance
 */
export async function hasAvailableBalance(
  organizationId: string,
  amount: number
): Promise<boolean> {
  try {
    const balance = await getOrganizationBalance(organizationId);
    return balance.availableBalance >= amount;
  } catch (error) {
    logger.error("Error checking available balance", { error, organizationId, amount });
    return false;
  }
}

/**
 * Update the organization balance based on the requested operation
 * @param organizationId Organization ID
 * @param amount Amount to update
 * @param operation Type of balance operation
 * @param transactionId Associated transaction ID (optional)
 * @param description Description of the operation (optional)
 * @returns The updated balance
 */
export async function updateOrganizationBalance(
  organizationId: string,
  amount: number,
  operation: BalanceOperationType,
  transactionId?: string,
  description?: string
) {
  try {
    // Basic validations
    if (amount <= 0) {
      throw new Error("Amount must be greater than zero");
    }

    // Use a transaction to ensure atomicity
    return await db.$transaction(async (tx) => {
      // Get current balance with lock to prevent race conditions
      let balance = await tx.organizationBalance.findUnique({
        where: { organizationId },
      });

      // If no balance record exists, create one with zero values
      if (!balance) {
        balance = await tx.organizationBalance.create({
          data: {
            organizationId,
            availableBalance: 0,
            pendingBalance: 0,
            reservedBalance: 0,
          }
        });
        logger.info("Created initial balance record", { organizationId });
      }

      // Calculate new balance values based on operation type
      let newAvailable = balance.availableBalance;
      let newPending = balance.pendingBalance;
      let newReserved = balance.reservedBalance;

      switch (operation) {
        case BalanceOperationType.CREDIT:
          // Add to available balance
          newAvailable += amount;
          break;

        case BalanceOperationType.DEBIT:
          // Remove from available balance
          if (balance.availableBalance < amount) {
            throw new Error(`Insufficient available balance: ${balance.availableBalance} < ${amount}`);
          }
          newAvailable -= amount;
          break;

        case BalanceOperationType.PENDING:
          // Add to pending balance
          newPending += amount;
          break;

        case BalanceOperationType.RELEASE:
          // Move from pending to available
          if (balance.pendingBalance < amount) {
            throw new Error(`Insufficient pending balance: ${balance.pendingBalance} < ${amount}`);
          }
          newPending -= amount;
          newAvailable += amount;
          break;

        case BalanceOperationType.RESERVE:
          // Move from available to reserved
          if (balance.availableBalance < amount) {
            throw new Error(`Insufficient available balance for reservation: ${balance.availableBalance} < ${amount}`);
          }
          newAvailable -= amount;
          newReserved += amount;
          break;

        case BalanceOperationType.UNRESERVE:
          // Move from reserved to available
          if (balance.reservedBalance < amount) {
            // Log detailed information for debugging
            logger.warn("Attempting to unreserve more than reserved balance", {
              organizationId,
              requestedAmount: amount,
              currentReserved: balance.reservedBalance,
              transactionId,
              operation: "UNRESERVE"
            });

            // Instead of throwing an error, adjust to the available reserved amount
            const adjustedAmount = Math.min(amount, balance.reservedBalance);
            if (adjustedAmount > 0) {
              newReserved -= adjustedAmount;
              newAvailable += adjustedAmount;
              logger.info("Adjusted unreserve amount to available reserved balance", {
                organizationId,
                requestedAmount: amount,
                adjustedAmount,
                transactionId
              });
            } else {
              logger.warn("No reserved balance to unreserve", {
                organizationId,
                requestedAmount: amount,
                currentReserved: balance.reservedBalance,
                transactionId
              });
              // Return current balance without changes
              return balance;
            }
          } else {
            newReserved -= amount;
            newAvailable += amount;
          }
          break;

        case BalanceOperationType.DEBIT_RESERVED:
          // Remove directly from reserved balance (for completed transfers)
          if (balance.reservedBalance < amount) {
            // Log detailed information for debugging
            logger.warn("Attempting to debit more than reserved balance", {
              organizationId,
              requestedAmount: amount,
              currentReserved: balance.reservedBalance,
              transactionId,
              operation: "DEBIT_RESERVED"
            });

            // Instead of throwing an error, adjust to the available reserved amount
            const adjustedAmount = Math.min(amount, balance.reservedBalance);
            if (adjustedAmount > 0) {
              newReserved -= adjustedAmount;
              logger.info("Adjusted debit amount to available reserved balance", {
                organizationId,
                requestedAmount: amount,
                adjustedAmount,
                transactionId
              });
            } else {
              logger.warn("No reserved balance to debit", {
                organizationId,
                requestedAmount: amount,
                currentReserved: balance.reservedBalance,
                transactionId
              });
              // Return current balance without changes
              return balance;
            }
          } else {
            newReserved -= amount;
          }
          break;

        default:
          throw new Error(`Invalid operation: ${operation}`);
      }

      // Ensure no negative balances
      if (newAvailable < 0 || newPending < 0 || newReserved < 0) {
        throw new Error(`Operation would result in negative balance: available=${newAvailable}, pending=${newPending}, reserved=${newReserved}`);
      }

      // Update the balance in the database
      const updatedBalance = await tx.organizationBalance.update({
        where: { organizationId },
        data: {
          availableBalance: newAvailable,
          pendingBalance: newPending,
          reservedBalance: newReserved,
        }
      });

      // Record the operation for audit
      await tx.balanceHistory.create({
        data: {
          organizationId,
          transactionId,
          operation,
          amount,
          description: description || `${operation} operation of ${amount}`,
          balanceAfterOperation: {
            available: updatedBalance.availableBalance,
            pending: updatedBalance.pendingBalance,
            reserved: updatedBalance.reservedBalance
          },
          balanceId: updatedBalance.id
        }
      });

      logger.info("Balance updated successfully", {
        organizationId,
        operation,
        amount,
        transactionId,
        previousBalance: {
          available: balance.availableBalance,
          pending: balance.pendingBalance,
          reserved: balance.reservedBalance
        },
        newBalance: {
          available: updatedBalance.availableBalance,
          pending: updatedBalance.pendingBalance,
          reserved: updatedBalance.reservedBalance
        }
      });

      return updatedBalance;
    });
  } catch (error) {
    // Rethrow with additional information
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      // Specific handling for Prisma errors
      logger.error("Prisma error updating balance", {
        code: error.code,
        meta: error.meta,
        organizationId,
        amount,
        operation,
        errorMessage: error.message
      });
    } else {
      // Garantir que não enviamos o objeto de erro diretamente para o logger
      // em vez disso, extraímos apenas as propriedades que precisamos
      const errorInfo = {
        organizationId,
        amount,
        operation,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        errorName: error instanceof Error ? error.name : "UnknownError",
        errorStack: error instanceof Error ? (error.stack?.split('\n')[0] || "") : ""
      };

      logger.error("Error updating organization balance", errorInfo);
    }
    throw error;
  }
}

/**
 * Process a transaction approval and update the organization balance
 * @param transaction The approved transaction
 * @returns The updated balance
 */
export async function processTransactionApproval(transaction: Transaction): Promise<void> {
  try {
    // Only process CHARGE transactions (incoming payments)
    if (transaction.type !== TransactionType.CHARGE) {
      logger.info("Ignoring balance update for non-CHARGE transaction", {
        transactionId: transaction.id,
        type: transaction.type
      });
      return;
    }

    // Verify the transaction is actually approved
    if (transaction.status !== TransactionStatus.APPROVED) {
      logger.warn("Attempt to process non-approved transaction", {
        transactionId: transaction.id,
        status: transaction.status
      });
      return;
    }

    logger.info("Processing balance update for approved transaction", {
      transactionId: transaction.id,
      organizationId: transaction.organizationId,
      amount: transaction.amount
    });

    // Usar o serviço centralizado para processar as taxas e atualizar o saldo
    const { processApprovedTransactionFees } = await import("../taxes/fee-service");
    await processApprovedTransactionFees(transaction);
  } catch (error) {
    logger.error("Error processing transaction approval", {
      error,
      transactionId: transaction.id,
      organizationId: transaction.organizationId
    });
    throw error;
  }
}

/**
 * Synchronize the organization balance with transactions
 * This is a maintenance function to ensure the balance is correct
 * @param organizationId Organization ID
 * @returns The synchronized balance
 */
export async function synchronizeOrganizationBalance(organizationId: string) {
  try {
    logger.info("Starting balance synchronization", { organizationId });

    // Get all approved transactions for the organization
    const approvedTransactions = await db.transaction.findMany({
      where: {
        organizationId,
        status: TransactionStatus.APPROVED,
      },
      select: {
        id: true,
        amount: true,
        type: true,
        createdAt: true,
        metadata: true,
        gatewayId: true,
        totalFee: true,
        percentFee: true,
        fixedFee: true
      },
    });

    logger.info("[DEBUG] Found approved transactions", {
      organizationId,
      count: approvedTransactions.length,
      transactions: approvedTransactions.map(t => ({
        id: t.id,
        amount: t.amount,
        type: t.type
      }))
    });

    // Get refunded transactions - transactions with status REFUNDED
    const refundedTransactions = await db.transaction.findMany({
      where: {
        organizationId,
        status: TransactionStatus.REFUNDED,
      },
      select: {
        id: true,
        amount: true,
        type: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    logger.info("[DEBUG] Found REFUNDED transactions", {
      organizationId,
      count: refundedTransactions.length,
      transactions: refundedTransactions.map(t => ({
        id: t.id,
        amount: t.amount,
        type: t.type
      }))
    });

    // Get transactions with REFUND type (estornos)
    const refundTransactions = await db.transaction.findMany({
      where: {
        organizationId,
        type: TransactionType.REFUND,
        status: TransactionStatus.APPROVED,
      },
      select: {
        id: true,
        amount: true,
        originalTransactionId: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    logger.info("[DEBUG] Found REFUND type transactions", {
      organizationId,
      count: refundTransactions.length,
      transactions: refundTransactions.map(t => ({
        id: t.id,
        amount: t.amount,
        originalTransactionId: t.originalTransactionId
      }))
    });

    // Calculate the correct balance
    let calculatedAvailableBalance = 0;
    let calculatedPendingBalance = 0;
    let calculatedReservedBalance = 0;

    // Process incoming transactions (CHARGE)
    for (const transaction of approvedTransactions.filter(t => t.type === TransactionType.CHARGE)) {
      // Usar o serviço centralizado para processar as taxas
      const { processTransactionFees } = await import("../taxes/fee-service");

      // Criar um objeto Transaction com os campos necessários
      const transactionObj = {
        id: transaction.id,
        organizationId,
        amount: transaction.amount,
        type: TransactionType.CHARGE,
        gatewayId: transaction.gatewayId,
      } as Transaction;

      const result = await processTransactionFees(transactionObj, 'CHARGE');

      // Add net amount to available balance if successful
      if (result.success) {
        calculatedAvailableBalance += result.netAmount;

        logger.info("[DEBUG] Adding transaction to calculated balance", {
          transactionId: transaction.id,
          amount: transaction.amount,
          fees: result.fees.totalFee,
          feeSource: result.fees.source || 'unknown',
          netAmount: result.netAmount,
          runningBalance: calculatedAvailableBalance
        });
      }
    }

    // Process outgoing transactions (SEND)
    for (const transaction of approvedTransactions.filter(t => t.type === TransactionType.SEND)) {
      // For SEND transactions, we need to subtract the transfer amount + fees
      const totalDeducted = transaction.amount + (transaction.totalFee || 0);
      calculatedAvailableBalance -= totalDeducted;

      logger.info("[DEBUG] Subtracting SEND transaction from calculated balance", {
        transactionId: transaction.id,
        transferAmount: transaction.amount,
        fees: transaction.totalFee || 0,
        totalDeducted,
        runningBalance: calculatedAvailableBalance
      });
    }

    // Process refunds using the combined refunds
    for (const refund of refundTransactions) {
      calculatedAvailableBalance -= refund.amount;
      logger.info("[DEBUG] Subtracting refund amount from balance during synchronization", {
        transactionId: refund.id,
        refundAmount: refund.amount,
        organizationId,
        runningBalance: calculatedAvailableBalance
      });
    }

    // Get pending transactions that have reserved balance
    const pendingTransactions = await db.transaction.findMany({
      where: {
        organizationId,
        status: "PENDING",
        type: "SEND"
      },
      select: {
        id: true,
        amount: true,
        metadata: true,
        gatewayId: true,
        gatewayName: true,
        totalFee: true,
        percentFee: true,
        fixedFee: true
      }
    });

    // Calculate reserved balance from pending transfers
    logger.info(`Found ${pendingTransactions.length} pending transactions for reserved balance calculation`, { organizationId });

    for (const transaction of pendingTransactions) {
      const metadata = transaction.metadata as any || {};

      // Calculate total amount including fees from database fields (preferred) or metadata fallback
      const totalFee = transaction.totalFee || 0;
      const totalAmount = transaction.amount + totalFee;

      // Log detailed information for debugging
      logger.info(`Reserved balance calculation for transaction ${transaction.id}`, {
        transactionId: transaction.id,
        amount: transaction.amount,
        totalFee,
        totalAmount,
        gatewayName: transaction.gatewayName,
        hasMetadata: !!metadata,
        metadataKeys: Object.keys(metadata || {}),
        metadataTotalAmount: metadata.totalAmount
      });

      calculatedReservedBalance += totalAmount;
    }

    // Get current balance
    const currentBalance = await getOrganizationBalance(organizationId);

    // If there's a discrepancy, update the balance
    if (currentBalance.availableBalance !== calculatedAvailableBalance ||
        currentBalance.pendingBalance !== calculatedPendingBalance ||
        currentBalance.reservedBalance !== calculatedReservedBalance) {

      logger.info("Balance discrepancy detected, updating balance", {
        organizationId,
        current: {
          available: currentBalance.availableBalance,
          pending: currentBalance.pendingBalance,
          reserved: currentBalance.reservedBalance
        },
        calculated: {
          available: calculatedAvailableBalance,
          pending: calculatedPendingBalance,
          reserved: calculatedReservedBalance
        }
      });

      // Update the balance in the database
      const updatedBalance = await db.organizationBalance.update({
        where: { organizationId },
        data: {
          availableBalance: calculatedAvailableBalance,
          pendingBalance: calculatedPendingBalance,
          reservedBalance: calculatedReservedBalance,
        }
      });

      // Record the operation for audit
      await db.balanceHistory.create({
        data: {
          organizationId,
          operation: "SYNC",
          amount: 0,
          description: `Balance synchronized by system`,
          balanceAfterOperation: {
            available: updatedBalance.availableBalance,
            pending: updatedBalance.pendingBalance,
            reserved: updatedBalance.reservedBalance
          },
          balanceId: updatedBalance.id
        }
      });

      return updatedBalance;
    }

    logger.info("Balance is already synchronized", { organizationId });
    return currentBalance;
  } catch (error) {
    logger.error("Error synchronizing organization balance", { error, organizationId });
    throw error;
  }
}

