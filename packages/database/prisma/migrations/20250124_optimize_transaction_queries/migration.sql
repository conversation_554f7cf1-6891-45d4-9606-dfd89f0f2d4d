-- Optimize transaction queries for better performance
-- Add composite indexes for duplicate checking and gateway selection

-- Index for duplicate checking by reference code
CREATE INDEX IF NOT EXISTS "transaction_referenceCode_idx" ON "transaction"("referenceCode");

-- Composite index for duplicate checking by customer email, amount, organization, type, and creation time
CREATE INDEX IF NOT EXISTS "transaction_duplicate_check_idx" ON "transaction"("customerEmail", "amount", "organizationId", "type", "createdAt");

-- Composite index for organization-specific queries
CREATE INDEX IF NOT EXISTS "transaction_org_status_type_idx" ON "transaction"("organizationId", "status", "type");

-- Index for customer email queries
CREATE INDEX IF NOT EXISTS "transaction_customerEmail_idx" ON "transaction"("customerEmail");

-- Composite index for time-based queries with organization
CREATE INDEX IF NOT EXISTS "transaction_org_created_idx" ON "transaction"("organizationId", "createdAt");

-- Index for payment gateway queries (if not already exists)
CREATE INDEX IF NOT EXISTS "payment_gateway_active_receive_idx" ON "payment_gateway"("isActive", "canReceive", "priority") WHERE "isActive" = true AND "canReceive" = true;

-- Index for organization gateway relationships
CREATE INDEX IF NOT EXISTS "organization_gateway_active_idx" ON "organization_gateway"("organizationId", "isActive", "isDefault") WHERE "isActive" = true;

-- Composite index for gateway selection with global flag
CREATE INDEX IF NOT EXISTS "payment_gateway_global_default_idx" ON "payment_gateway"("isGlobal", "isDefault", "isActive", "canReceive", "priority") WHERE "isActive" = true AND "canReceive" = true;
