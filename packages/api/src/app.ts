import { getBaseUrl } from "@repo/utils";
import { apiReference } from "@scalar/hono-api-reference";
import { Hono } from "hono";
import { openAPISpecs } from "hono-openapi";
import { corsMiddleware } from "./middleware/cors";
import { loggerMiddleware } from "./middleware/logger";
import { adminRouter } from "./routes/admin/router";
import { authRouter } from "./routes/auth";
import { healthRouter } from "./routes/health";
import { organizationsRouter } from "./routes/organizations";
import { paymentsRouter } from "./routes/payments/router";
import { webhooksRouter } from "./routes/webhooks";
import { dashboardRouter } from "./routes/dashboard";
import { apiKeysRouter } from "./routes/api-keys/router";
import { transactionsRouter } from "./routes/payments/transactions/router";
import { transfersRouter } from "./routes/payments/transfers/router";
import { authMiddleware } from "./middleware/auth";
import { twoFactorMiddleware } from "./middleware/two-factor";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { z } from "zod";

export const app = new Hono().basePath("/api");

app.use(loggerMiddleware);
app.use(corsMiddleware);

// Mount webhooks router directly to handle incoming webhooks without auth
app.route("/webhooks", webhooksRouter);

// Todas as rotas são montadas em "/" porque o basePath já foi definido como "/api"
const appRouter = app
	// Rota de autenticação deve ser a primeira para não ser sobrescrita
	.route("/", authRouter)
	// Outras rotas vêm depois
	.route("/", apiKeysRouter)
	.route("/", paymentsRouter)
	.route("/", organizationsRouter)
	.route("/", adminRouter)
	.route("/", healthRouter)
	.route("/", dashboardRouter);

// Registramos novamente a rota de autenticação para garantir que ela tenha prioridade
app.get("/auth/*", (c) => {
	return authRouter.fetch(c.req.raw, c.env);
});

app.post("/auth/*", (c) => {
	return authRouter.fetch(c.req.raw, c.env);
});

// Create a separate app for documentation with only the public routes
const docsApp = new Hono().basePath("/api");
// No webhooks in the documentation

// Create simplified transaction router for documentation
const docsTransactionsRouter = new Hono<{ Variables: { session: any; user: any } }>();

// Create a new transaction (Pix payment)
docsTransactionsRouter.post(
  "/",
  authMiddleware,
  validator("json", z.object({
    amount: z.number().positive(),
    customerName: z.string().min(1),
    customerEmail: z.string().email(),
    customerPhone: z.string().optional(),
    customerDocument: z.string().optional(),
    customerDocumentType: z.enum(["cpf", "cnpj"]).optional(),
    description: z.string().optional(),
    metadata: z.record(z.any()).optional(),
    organizationId: z.string(),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "Create a new transaction (Pix payment)",
    description: `Creates a new PIX payment transaction.

## Webhooks PIX

Quando o status de uma transação PIX muda, um webhook é enviado para a URL configurada. Os possíveis status são:

- **PENDING**: Transação criada, aguardando pagamento
- **PROCESSING**: Transação em processamento
- **APPROVED**: Transação aprovada/paga com sucesso
- **REJECTED**: Transação rejeitada/falhou
- **CANCELED**: Transação cancelada
- **REFUNDED**: Transação estornada

### Eventos PIX de Webhook

- **pix.in.processing**: Enviado quando um PIX recebido está sendo processado
- **pix.in.confirmation**: Enviado quando um PIX recebido é confirmado (status = APPROVED)
- **pix.in.reversal.processing**: Enviado quando um estorno de PIX recebido está sendo processado
- **pix.in.reversal.confirmation**: Enviado quando um estorno de PIX recebido é confirmado`,
    responses: {
      201: { description: "Transaction created successfully" },
      400: { description: "Invalid request data" },
      401: { description: "Unauthorized" },
      403: { description: "Forbidden" },
      500: { description: "Internal server error" },
    },
  }),
  () => new Response("Documentation only")
);

// List transactions
docsTransactionsRouter.get(
  "/",
  authMiddleware,
  validator("query", z.object({
    organizationId: z.string(),
    status: z.enum(["PENDING", "APPROVED", "REJECTED", "CANCELED", "PROCESSING", "REFUNDED", "BLOCKED"]).optional(),
    type: z.enum(["CHARGE", "SEND", "RECEIVE"]).optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    page: z.string().transform(Number).default("1"),
    limit: z.string().transform(Number).default("10"),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "List transactions",
    description: "Lists transactions with optional filters",
    responses: {
      200: { description: "Transactions retrieved successfully" },
      401: { description: "Unauthorized" },
      403: { description: "Forbidden" },
      500: { description: "Internal server error" },
    },
  }),
  () => new Response("Documentation only")
);

// Get transaction by ID
docsTransactionsRouter.get(
  "/:id",
  authMiddleware,
  validator("param", z.object({
    id: z.string(),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "Get transaction by ID",
    description: "Retrieves a transaction by its ID",
    responses: {
      200: { description: "Transaction retrieved successfully" },
      401: { description: "Unauthorized" },
      403: { description: "Forbidden" },
      404: { description: "Transaction not found" },
      500: { description: "Internal server error" },
    },
  }),
  () => new Response("Documentation only")
);

// Create simplified transfers router for documentation
const docsTransfersRouter = new Hono<{ Variables: { session: any; user: any } }>();

// Create a new PIX transfer
docsTransfersRouter.post(
  "/pix",
  authMiddleware,
  twoFactorMiddleware,
  validator("json", z.object({
    amount: z.number().positive(),
    pixKey: z.string(),
    pixKeyType: z.enum(["CPF", "CNPJ", "EMAIL", "PHONE", "RANDOM"]),
    description: z.string().optional(),
    organizationId: z.string(),
  })),
  describeRoute({
    tags: ["Transfers"],
    summary: "Create a new PIX transfer",
    description: `Creates a new PIX transfer to a PIX key.

## Webhooks PIX

Quando o status de uma transferência PIX muda, um webhook é enviado para a URL configurada. Os possíveis status são:

- **PENDING**: Transferência criada, aguardando processamento
- **PROCESSING**: Transferência em processamento
- **APPROVED**: Transferência aprovada/concluída com sucesso
- **REJECTED**: Transferência rejeitada/falhou
- **CANCELED**: Transferência cancelada

### Eventos PIX de Webhook

- **pix.out.processing**: Enviado quando uma transferência PIX está sendo processada
- **pix.out.confirmation**: Enviado quando uma transferência PIX é confirmada (status = APPROVED)
- **pix.out.failure**: Enviado quando uma transferência PIX falha (status = REJECTED/CANCELED)
- **pix.out.reversal**: Enviado quando uma transferência PIX é estornada`,
    responses: {
      201: { description: "Transfer created successfully" },
      400: { description: "Invalid request data" },
      401: { description: "Unauthorized" },
      403: { description: "Forbidden" },
      500: { description: "Internal server error" },
    },
  }),
  () => new Response("Documentation only")
);

// List transfers
docsTransfersRouter.get(
  "/",
  authMiddleware,
  validator("query", z.object({
    organizationId: z.string(),
    status: z.string().optional(),
    search: z.string().optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    page: z.string().transform(Number).default("1"),
    limit: z.string().transform(Number).default("10"),
  })),
  describeRoute({
    tags: ["Transfers"],
    summary: "List transfers",
    description: "Lists transfers with optional filters",
    responses: {
      200: { description: "Transfers retrieved successfully" },
      401: { description: "Unauthorized" },
      403: { description: "Forbidden" },
      500: { description: "Internal server error" },
    },
  }),
  () => new Response("Documentation only")
);

// Get transfer by ID
docsTransfersRouter.get(
  "/:id",
  authMiddleware,
  validator("param", z.object({
    id: z.string(),
  })),
  describeRoute({
    tags: ["Transfers"],
    summary: "Get transfer by ID",
    description: "Retrieves a transfer by its ID",
    responses: {
      200: { description: "Transfer retrieved successfully" },
      401: { description: "Unauthorized" },
      403: { description: "Forbidden" },
      404: { description: "Transfer not found" },
      500: { description: "Internal server error" },
    },
  }),
  () => new Response("Documentation only")
);

// Only include specific payment routes with the cleaned-up routers
const publicPaymentsRouter = new Hono()
	.basePath("/payments")
	.route("/transactions", docsTransactionsRouter)
	.route("/transfers", docsTransfersRouter);

docsApp.route("/", publicPaymentsRouter);

app.get(
	"/openapi",
	openAPISpecs(docsApp, {
		documentation: {
			info: {
				title: "Pluggou API",
				version: "1.0.0",
				description: `
API para plataforma de pagamentos focada em PIX

## Autenticação

Todas as requisições à API devem ser autenticadas usando uma chave de API.

Para autenticar suas requisições:
1. Inclua sua chave de API no cabeçalho \`X-API-Key\`
2. Inclua o ID da sua organização nos parâmetros da requisição (\`organizationId\`)

Exemplo:
\`\`\`
curl -X GET "${getBaseUrl()}/api/payments/transactions?organizationId=YOUR_ORGANIZATION_ID" \\
  -H "X-API-Key: pk_test_AbCdEfGhIjKlMnOpQrStUvWxYz" \\
  -H "Content-Type: application/json"
\`\`\`

## Endpoints disponíveis

- GET /api/payments/transactions - Listar transações PIX
- GET /api/payments/transactions/{id} - Buscar transação PIX por ID
- POST /api/payments/transactions - Criar nova transação PIX
- GET /api/payments/transfers - Listar transferências PIX
- GET /api/payments/transfers/{id} - Buscar transferência PIX por ID
- POST /api/payments/transfers/pix - Criar nova transferência PIX

Para obter uma chave de API, acesse a seção de Integrações > API no painel administrativo.

## Webhooks

A API Pluggou oferece webhooks para notificar sua aplicação sobre eventos importantes relacionados a transações PIX.

### Eventos PIX Disponíveis

#### PIX Recebidos
- **\`pix.in.processing\`** - PIX recebido está sendo processado
- **\`pix.in.confirmation\`** - PIX recebido foi confirmado com sucesso

#### PIX Enviados
- **\`pix.out.processing\`** - PIX enviado está sendo processado
- **\`pix.out.confirmation\`** - PIX enviado foi confirmado com sucesso
- **\`pix.out.failure\`** - PIX enviado falhou (rejeitado, bloqueado ou cancelado)

#### Estornos PIX
- **\`pix.in.reversal.processing\`** - Estorno de PIX recebido está sendo processado
- **\`pix.in.reversal.confirmation\`** - Estorno de PIX recebido foi confirmado
- **\`pix.out.reversal\`** - PIX enviado foi estornado

### Estrutura do Payload

Todos os webhooks PIX seguem a mesma estrutura de payload:

\`\`\`json
{
  "id": "cmb2rephm0001yo103xz9i0gm",
  "type": "pix.in.confirmation",
  "created_at": "2024-01-15T10:30:45.000Z",
  "data": {
    "id": "cmb2rephm0001yo103xz9i0gm",
    "type": "CHARGE",
    "status": "APPROVED",
    "amount": 100.00,
    "pixKey": "<EMAIL>",
    "pixKeyType": "EMAIL",
    "description": "Pagamento PIX",
    "customerName": "João Silva",
    "customerEmail": "<EMAIL>",
    "customerDocument": "12345678901",
    "createdAt": "2024-01-15T10:30:00Z",
    "paymentAt": "2024-01-15T10:30:45Z",
    "organizationId": "org_123456789",
    "endToEndId": "E12345678202401151030ABCDEF123456",
    "externalId": "ext_abc123",
    "referenceCode": "REF001",
    "percentFee": 0,
    "fixedFee": 0.50,
    "totalFee": 0.50,
    "netAmount": 99.50,
    "previousStatus": "PROCESSING"
  }
}
\`\`\`

### Configuração de Webhooks

Para configurar webhooks:

1. Acesse **Integrações > Webhooks** no painel administrativo
2. Adicione a URL do seu endpoint
3. Selecione os eventos PIX que deseja receber
4. Ative o webhook

### Segurança

Todos os webhooks são enviados via HTTPS e incluem assinatura para verificação de autenticidade.
Verifique sempre a assinatura do webhook antes de processar os dados.

### Retry Policy

Em caso de falha na entrega, os webhooks são reenviados automaticamente com backoff exponencial.
`,
			},
			servers: [
				{
					url: `${getBaseUrl()}`,
					description: "API server",
				},
			],
			components: {
				securitySchemes: {
					ApiKeyAuth: {
						type: "apiKey",
						in: "header",
						name: "X-API-Key",
						description: "API key authentication"
					}
				}
			},
			security: [
				{
					ApiKeyAuth: []
				}
			]
		},
	}),
);

app.get(
	"/docs",
	apiReference({
		theme: "saturn",
		spec: {
			url: "/api/openapi",
		},
	}),
);

export type AppRouter = typeof appRouter;
