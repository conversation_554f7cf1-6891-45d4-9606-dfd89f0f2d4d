import { getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { getPaymentProvider } from "@repo/payments";
import { PixTransferService } from "@repo/payments/src/transfers/pix-transfer-service";
import { Prisma } from "@prisma/client";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { authMiddleware } from "../../../middleware/auth";
import { twoFactorMiddleware } from "../../../middleware/two-factor";

// Importante: Usamos basePath para evitar conflitos com outras rotas
export const transfersRouter = new Hono()
  // Não precisamos definir basePath aqui, pois já está definido no paymentsRouter
  .use("/*", authMiddleware);

// Create a new PIX transfer
transfersRouter.post(
  "/pix",
  authMiddleware as any, // Cast to any to bypass type issues temporarily
  async (c, next) => {
    // Check if using API key authentication - if yes, skip 2FA
    const apiKey = c.get("apiKey");
    if (apiKey) {
      // API key access doesn't require 2FA
      return await next();
    }

    // Not using API key, so we need to enforce 2FA
    await twoFactorMiddleware(c, next);
  },
  validator("json", z.object({
    amount: z.number().positive(),
    pixKey: z.string(),
    pixKeyType: z.enum(["CPF", "CNPJ", "EMAIL", "PHONE", "RANDOM"]),
    description: z.string().optional(),
    organizationId: z.string(),
  })),
  describeRoute({
    tags: ["Transfers"],
    summary: "Create a new PIX transfer",
    description: "Creates a new PIX transfer to a PIX key",
    responses: {
      201: {
        description: "Transfer created successfully",
      },
      400: {
        description: "Invalid request data",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    const {
      amount,
      pixKey,
      pixKeyType,
      description,
      organizationId
    } = c.req.valid("json");
    const session = c.get("session");

    try {
      // Check if using API key authentication
      const apiKey = c.get("apiKey");
      const organization = c.get("organization");

      // If using API key, verify the organization matches
      if (apiKey && organization) {
        if (organization.id !== organizationId) {
          throw new HTTPException(403, { message: "API key does not have access to this organization" });
        }
        // API key is authorized for this organization - continue with the transfer
        logger.info("Processing PIX transfer via API key", {
          organizationId,
          apiKeyId: apiKey.id
        });
      } else {
        // Verify organization membership for regular users
        const membership = await getOrganizationMembership(session.userId, organizationId);
        if (!membership) {
          throw new HTTPException(403, { message: "You don't have access to this organization" });
        }
      }

      // Validate the key format based on type
      const validateKey = (key: string, type: string): boolean => {
        switch (type) {
          case "CPF":
            return /^\d{11}$/.test(key);
          case "CNPJ":
            return /^\d{14}$/.test(key);
          case "EMAIL":
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(key);
          case "PHONE":
            return /^\+\d{1,3}\d{8,14}$/.test(key);
          case "RANDOM":
            return /^[a-zA-Z0-9]{32,36}$/.test(key);
          default:
            return false;
        }
      };

      if (!validateKey(pixKey, pixKeyType)) {
        throw new HTTPException(400, { message: `Invalid format for Pix key type: ${pixKeyType}` });
      }

      // Use the shared PIX transfer service
      const transferResult = await PixTransferService.processTransfer({
        amount,
        pixKey,
        pixKeyType,
        organizationId,
        description,
        metadata: {
          source: apiKey ? "api_key" : "web_interface",
          apiKeyId: apiKey?.id,
          userId: session?.userId
        },
        userId: session?.userId,
        requiresTwoFactor: !apiKey // API keys don't require 2FA
      });

      // Trigger webhook events for the transaction
      try {
        const { triggerTransactionEvents } = await import("@repo/payments/src/webhooks/events");
        const transaction = await db.transaction.findUnique({
          where: { id: transferResult.id }
        });
        if (transaction) {
          await triggerTransactionEvents(transaction);
          logger.info("Webhook events triggered for transfer transaction", { transactionId: transferResult.id });
        }
      } catch (webhookError) {
        logger.error("Error triggering webhook events for transfer", {
          error: webhookError,
          transactionId: transferResult.id
        });
        // Don't interrupt the flow if webhook fails
      }

      return c.json({
        id: transferResult.id,
        status: transferResult.status,
        amount: transferResult.amount,
        pixKey: transferResult.pixKey,
        pixKeyType: transferResult.pixKeyType,
        description,
        externalId: transferResult.externalId,
        fee: transferResult.totalFee,
        totalAmount: transferResult.totalAmount,
        gatewayType: transferResult.gatewayType,
        message: transferResult.message
      }, 201);
    } catch (error) {
      logger.error("Error creating PIX transfer", { error });

      // If it's already an HTTPException, re-throw it
      if (error instanceof HTTPException) {
        throw error;
      }

      // For other errors, throw a generic error
      throw new HTTPException(500, {
        message: error instanceof Error ? error.message : "Failed to process PIX transfer"
      });
    }
  }
);



// List transfers
transfersRouter.get(
  "/",
  validator("query", z.object({
    organizationId: z.string(),
    status: z.string().optional(),
    search: z.string().optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    page: z.string().transform(Number).default("1"),
    limit: z.string().transform(Number).default("10"),
  })),
  describeRoute({
    tags: ["Transfers"],
    summary: "List transfers",
    description: "Lists transfers with optional filters",
    responses: {
      200: {
        description: "Transfers retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const {
        organizationId,
        status,
        search,
        startDate,
        endDate,
        page,
        limit
      } = c.req.valid("query");

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this organization" });
      }

      // Build the query
      const where: Prisma.TransactionWhereInput = {
        organizationId,
        type: "SEND", // Only include transfers
      };

      if (status && status !== "all") {
        where.status = status.toUpperCase() as any;
      }

      if (search) {
        where.OR = [
          { id: { contains: search, mode: Prisma.QueryMode.insensitive } },
          { externalId: { contains: search, mode: Prisma.QueryMode.insensitive } },
          { referenceCode: { contains: search, mode: Prisma.QueryMode.insensitive } },
          { customerName: { contains: search, mode: Prisma.QueryMode.insensitive } },
          { pixKey: { contains: search, mode: Prisma.QueryMode.insensitive } },
        ];
      }

      if (startDate || endDate) {
        where.createdAt = {} as Prisma.DateTimeFilter;

        if (startDate) {
          (where.createdAt as Prisma.DateTimeFilter).gte = new Date(startDate);
        }

        if (endDate) {
          (where.createdAt as Prisma.DateTimeFilter).lte = new Date(endDate);
        }
      }

      // Get total count
      const total = await db.transaction.count({ where });

      // Get transfers (gateway information removed as requested)
      const transfers = await db.transaction.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
        skip: (page - 1) * limit,
        take: limit,
      });

      return c.json({
        data: transfers.map(tx => ({
          id: tx.id,
          externalId: tx.externalId,
          referenceCode: tx.referenceCode,
          amount: tx.amount,
          status: tx.status,
          description: tx.description,
          pixKey: tx.pixKey,
          pixKeyType: tx.pixKeyType,
          date: tx.createdAt.toISOString(),
          paymentDate: tx.paymentAt?.toISOString(),
          recipient: {
            name: tx.customerName,
            pixKey: tx.pixKey,
            pixKeyType: tx.pixKeyType,
          },
          // Gateway information removed as requested
        })),
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit),
        },
      });
    } catch (error) {
      logger.error("Error listing transfers", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to list transfers" });
    }
  }
);

// Get transfers summary
transfersRouter.get(
  "/summary",
  validator("query", z.object({
    organizationId: z.string(),
  })),
  describeRoute({
    tags: ["Transfers"],
    summary: "Get transfers summary",
    description: "Gets summary statistics for transfers",
    responses: {
      200: {
        description: "Summary retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { organizationId } = c.req.valid("query");

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this organization" });
      }

      // Obter total de transferências enviadas (SEND)
      const outgoingTransfers = await db.transaction.findMany({
        where: {
          organizationId,
          type: "SEND",
        },
        select: {
          amount: true,
          createdAt: true,
        },
      });

      // Obter total de transferências recebidas (RECEIVE)
      const incomingTransfers = await db.transaction.findMany({
        where: {
          organizationId,
          type: "RECEIVE",
        },
        select: {
          amount: true,
          createdAt: true,
        },
      });

      // Calcular volume total
      const totalVolume = [...outgoingTransfers, ...incomingTransfers].reduce(
        (sum, tx) => sum + tx.amount,
        0
      );

      // Calcular valor médio
      const averageValue = totalVolume / (outgoingTransfers.length + incomingTransfers.length || 1);

      // Calcular crescimento (mock - em uma implementação real, compararia com período anterior)
      // Aqui estamos usando valores fixos para demonstração
      const totalVolumeGrowth = 12.5;
      const incomingTransfersGrowth = 8.3;
      const outgoingTransfersGrowth = -4.5;
      const averageValueGrowth = 7.2;

      // Montar a resposta
      const data = {
        totalVolume: {
          amount: totalVolume,
          growth: totalVolumeGrowth,
        },
        incomingTransfers: {
          count: incomingTransfers.length,
          growth: incomingTransfersGrowth,
        },
        outgoingTransfers: {
          count: outgoingTransfers.length,
          growth: outgoingTransfersGrowth,
        },
        averageValue: {
          amount: averageValue,
          growth: averageValueGrowth,
        },
      };

      return c.json(data);
    } catch (error) {
      logger.error("Error getting transfers summary", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to get transfers summary" });
    }
  }
);

// Sync transfer status with gateway
transfersRouter.post(
  "/sync-status",
  validator("json", z.object({
    transactionId: z.string(),
    organizationId: z.string(),
  })),
  describeRoute({
    tags: ["Transfers"],
    summary: "Sync transfer status with gateway",
    description: "Manually syncs a transfer's status with the payment gateway",
    responses: {
      200: {
        description: "Transfer status synced successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Transfer not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { transactionId, organizationId } = c.req.valid("json");

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this organization" });
      }

      // Find the transaction
      const transaction = await db.transaction.findUnique({
        where: { id: transactionId },
        include: {
          gateway: true,
        },
      });

      if (!transaction) {
        throw new HTTPException(404, { message: "Transfer not found" });
      }

      // Verify the transaction belongs to the organization
      if (transaction.organizationId !== organizationId) {
        throw new HTTPException(403, { message: "You don't have access to this transfer" });
      }

      // Get the payment provider that processed this transaction
      const provider = await getPaymentProvider(organizationId, {
        forceType: transaction.gateway?.type,
        action: 'status'
      });

      if (!provider) {
        throw new HTTPException(400, { message: "Payment provider not found" });
      }

      // Check the status with the gateway
      const statusResult = await provider.getTransactionStatus({
        transactionId: transaction.externalId || transaction.id,
        organizationId: transaction.organizationId,
      });

      // Map the status
      let mappedStatus = transaction.status;
      if (statusResult.mappedStatus) {
        // If the provider returns a mapped status, use it directly
        mappedStatus = statusResult.mappedStatus;
      } else if (statusResult.status) {
        // Fallback mapping
        const statusMap: Record<string, string> = {
          "PENDING": "PENDING",
          "APPROVED": "APPROVED",
          "CONFIRMED": "APPROVED",
          "RECEIVED": "APPROVED",
          "REJECTED": "REJECTED",
          "CANCELED": "CANCELED",
          "PROCESSING": "PROCESSING",
          "REFUNDED": "REFUNDED",
        };
        mappedStatus = (statusMap[statusResult.status.toUpperCase()] || transaction.status) as any;
      }

      // Update the transaction if the status has changed
      let updatedTransaction = transaction;
      if (mappedStatus !== transaction.status) {
        const isCompleted = mappedStatus === "APPROVED" ||
                          mappedStatus === "REFUNDED";

        updatedTransaction = await db.transaction.update({
          where: { id: transactionId },
          data: {
            status: mappedStatus,
            paymentAt: isCompleted ? new Date() : transaction.paymentAt,
          },
          include: {
            gateway: true,
          },
        });
      }

      return c.json({
        success: true,
        transfer: {
          id: updatedTransaction.id,
          externalId: updatedTransaction.externalId,
          status: updatedTransaction.status,
          amount: updatedTransaction.amount,
          pixKey: updatedTransaction.pixKey,
          pixKeyType: updatedTransaction.pixKeyType,
          date: updatedTransaction.createdAt.toISOString(),
          paymentDate: updatedTransaction.paymentAt?.toISOString(),
          updatedAt: updatedTransaction.updatedAt.toISOString(),
          description: updatedTransaction.description,
          recipient: {
            name: updatedTransaction.customerName,
            pixKey: updatedTransaction.pixKey,
            pixKeyType: updatedTransaction.pixKeyType,
          },
          gateway: updatedTransaction.gateway ? {
            name: updatedTransaction.gateway.name,
            type: updatedTransaction.gateway.type,
          } : null,
        },
        previousStatus: transaction.status,
        newStatus: updatedTransaction.status,
        message: mappedStatus !== transaction.status
          ? "Transfer status updated successfully"
          : "Transfer status is already up to date",
      });
    } catch (error) {
      logger.error("Error syncing transfer status", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to sync transfer status" });
    }
  }
);

// Get transfer by ID
transfersRouter.get(
  "/:id",
  validator("param", z.object({
    id: z.string(),
  })),
  describeRoute({
    tags: ["Transfers"],
    summary: "Get transfer by ID",
    description: "Retrieves a transfer by its ID",
    responses: {
      200: {
        description: "Transfer retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Transfer not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { id } = c.req.valid("param");

      const transaction = await db.transaction.findUnique({
        where: { id },
        include: {
          gateway: true,
        },
      });

      if (!transaction) {
        throw new HTTPException(404, { message: "Transfer not found" });
      }

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, transaction.organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this transfer" });
      }

      // If the transaction is in a processing state, check its status with the gateway
      if (transaction.status === "PROCESSING" && transaction.externalId) {
        try {
          const provider = await getPaymentProvider(transaction.organizationId);
          const statusResult = await provider.getTransactionStatus({
            transactionId: transaction.externalId,
            organizationId: transaction.organizationId,
          });

          // Map the status
          let mappedStatus = transaction.status;
          if (statusResult.mappedStatus) {
            // If the provider returns a mapped status, use it directly
            mappedStatus = statusResult.mappedStatus;
          } else if (statusResult.status) {
            // Fallback mapping
            const statusMap: Record<string, string> = {
              "PENDING": "PENDING",
              "APPROVED": "APPROVED",
              "CONFIRMED": "APPROVED",
              "RECEIVED": "APPROVED",
              "REJECTED": "REJECTED",
              "CANCELED": "CANCELED",
              "PROCESSING": "PROCESSING",
              "REFUNDED": "REFUNDED",
            };
            mappedStatus = (statusMap[statusResult.status.toUpperCase()] || transaction.status) as any;
          }

          // Update the transaction if the status has changed
          if (mappedStatus !== transaction.status) {
            const isCompleted = mappedStatus === "APPROVED" ||
                              mappedStatus === "REFUNDED";

            await db.transaction.update({
              where: { id },
              data: {
                status: mappedStatus,
                paymentAt: isCompleted ? new Date() : transaction.paymentAt,
              },
            });

            // Refresh the transaction data
            transaction.status = mappedStatus;
            if (isCompleted) {
              transaction.paymentAt = new Date();
            }
          }
        } catch (error) {
          logger.error("Error checking transfer status", { error, transactionId: id });
          // Continue with the current transaction data
        }
      }

      return c.json({
        id: transaction.id,
        externalId: transaction.externalId,
        referenceCode: transaction.referenceCode,
        amount: transaction.amount,
        status: transaction.status,
        type: transaction.type,
        description: transaction.description,
        pixKey: transaction.pixKey,
        pixKeyType: transaction.pixKeyType,
        createdAt: transaction.createdAt,
        paymentAt: transaction.paymentAt,
        updatedAt: transaction.updatedAt,
        gateway: transaction.gateway ? {
          id: transaction.gateway.id,
          name: transaction.gateway.name,
          type: transaction.gateway.type,
        } : null,
      });
    } catch (error) {
      logger.error("Error getting transfer", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to get transfer" });
    }
  }
);
