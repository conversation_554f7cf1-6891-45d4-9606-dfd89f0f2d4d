import { app } from "@repo/api";
import { handle } from "hono/vercel";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest } from "next/server";
import { hasAvailableBalance, updateOrganizationBalance, BalanceOperationType } from "@repo/payments/src/balance/balance-service";
import { processTransferFees } from "@repo/payments/src/taxes/fee-service";

// Handler para a API do Hono
const handler = handle(app);

// Export para os métodos padrão do Hono
export const GET = handler;
// Removemos o POST daqui, pois implementamos nossa própria função
export const PUT = handler;
export const PATCH = handler;
export const DELETE = handler;
export const OPTIONS = handler;

/**
 * Endpoint customizado para processar transferências com verificação de saldo
 * POST /api/payments/transfers
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  logger.info("Next.js /api/payments/transfers POST handler started", { url: request.url });
  try {
    // Verificar se é uma chamada para o endpoint do Hono
    const url = new URL(request.url);
    const pathname = url.pathname;

    // Se o caminho tiver algum sufixo como /api/payments/transfers/status,
    // usamos o handler do Hono
    if (pathname !== "/api/payments/transfers") {
      return handler(request);
    }

    // Obter a sessão do usuário
    const { getSession } = await import("@saas/auth/lib/server");
    const session = await getSession();
    if (!session) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Obter dados da transferência
    const data = await request.json();
    const {
      organizationId,
      amount,
      pixKey,
      pixKeyType,
      description,
      destinationName,
      destinationDocument
    } = data;

    if (!organizationId || !amount || !pixKey || !pixKeyType) {
      return Response.json({
        error: "Missing required fields",
        requiredFields: ["organizationId", "amount", "pixKey", "pixKeyType"]
      }, { status: 400 });
    }

    // Verificar se o valor é válido (positivo e maior que zero)
    if (amount <= 0) {
      return Response.json({ error: "Amount must be greater than zero" }, { status: 400 });
    }

    // Verificar se o usuário tem acesso à organização
    const membership = await db.member.findUnique({
      where: {
        userId_organizationId: {
          userId: session.user.id,
          organizationId,
        },
      },
    });

    if (!membership) {
      logger.warn("Unauthorized access attempt to create transfer", {
        userId: session.user.id,
        organizationId
      });
      return Response.json({ error: "Unauthorized for this organization" }, { status: 403 });
    }

    // Verificar se a organização está com status APPROVED
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { status: true }
    });

    if (!organization) {
      return Response.json({ error: "Organization not found" }, { status: 404 });
    }

    if (organization.status !== "APPROVED") {
      return Response.json({
        error: "Organization is not approved",
        status: organization.status
      }, { status: 403 });
    }

    // Gerar código de referência único para a transação
    const referenceCode = `TR${Date.now()}${Math.floor(Math.random() * 1000)}`;

    // Iniciar a transferência em status pendente
    const transaction = await db.transaction.create({
      data: {
        type: "SEND",
        status: "PENDING",
        amount,
        description: description || `Transferência PIX para ${pixKeyType} ${pixKey}`,
        referenceCode,
        pixKey,
        pixKeyType,
        customerName: destinationName || "Não informado",
        customerDocument: destinationDocument,
        customerEmail: session.user.email || "", // Campo obrigatório
        organizationId
      }
    });

    logger.info("Transfer initiated", {
      transactionId: transaction.id,
      organizationId,
      amount,
      pixKey,
      pixKeyType
    });

    // Processar as taxas da transferência
    logger.info("Processing transfer fees", {
      transactionId: transaction.id,
      organizationId,
      amount
    });

    const feesResult = await processTransferFees(transaction);

    if (!feesResult.success) {
      logger.error("Failed to process transfer fees", {
        transactionId: transaction.id,
        organizationId
      });

      // Atualizar o status da transação para REJECTED
      await db.transaction.update({
        where: { id: transaction.id },
        data: { status: "REJECTED" }
      });

      return Response.json({
        error: "Failed to process transfer fees",
        status: "REJECTED",
        transactionId: transaction.id
      }, { status: 500 });
    }

    // Obter o valor total (transferência + taxas)
    const totalAmount = feesResult.totalAmount;

    // Verificar se a organização tem saldo disponível suficiente para o valor total
    const hasSufficientBalance = await hasAvailableBalance(organizationId, totalAmount);
    if (!hasSufficientBalance) {
      logger.warn("Transfer rejected due to insufficient balance", {
        organizationId,
        amount,
        totalAmount,
        fees: feesResult.fees.totalFee,
        userId: session.user.id
      });

      // Atualizar o status da transação para REJECTED
      await db.transaction.update({
        where: { id: transaction.id },
        data: { status: "REJECTED" }
      });

      return Response.json({
        error: "Insufficient balance for this transfer (including fees)",
        status: "REJECTED",
        transactionId: transaction.id,
        amount,
        fees: feesResult.fees.totalFee,
        totalAmount
      }, { status: 400 });
    }

    // Reservar o valor total (transferência + taxas) no saldo da organização
    await updateOrganizationBalance(
      organizationId,
      totalAmount, // Usar o valor total incluindo taxas
      BalanceOperationType.RESERVE,
      transaction.id,
      `Reserva para transferência PIX: ${transaction.id} (valor: ${amount}, taxa: ${feesResult.fees.totalFee})`
    );

    logger.info("Balance reserved for transfer", {
      transactionId: transaction.id,
      organizationId,
      amount,
      fees: feesResult.fees.totalFee,
      totalAmount
    });

    // Posteriormente, esse processo será completado por um webhook ou cron job
    // que atualizará o status da transação para APPROVED ou REJECTED

    logger.info("Next.js /api/payments/transfers POST handler finished", { duration: Date.now() - startTime });
    return Response.json({
      success: true,
      transactionId: transaction.id,
      referenceCode,
      status: transaction.status,
      amount,
      fees: {
        percentFee: feesResult.fees.percentFee,
        fixedFee: feesResult.fees.fixedFee,
        totalFee: feesResult.fees.totalFee
      },
      totalAmount: feesResult.totalAmount,
      message: "Transfer initiated and will be processed"
    }, { status: 201 });
  } catch (error) {
    logger.error("Error processing transfer:", error, { duration: Date.now() - startTime });
    return Response.json({
      error: "Internal Server Error",
      message: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}


