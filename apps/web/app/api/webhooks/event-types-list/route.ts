import { NextRequest, NextResponse } from "next/server";
import { WebhookEventType } from "@repo/payments/src/webhooks/events";

export async function GET(req: NextRequest) {
  try {
    // Criar uma lista de tipos de eventos com descrições detalhadas
    const eventTypeDescriptions = [
      // PIX Incoming Events
      {
        type: WebhookEventType.PIX_IN_PROCESSING,
        title: "PIX Recebido - Processando",
        description: "Acionado quando um PIX de entrada está sendo processado pelo sistema",
        detailedDescription: "Este evento é disparado imediatamente quando um PIX é recebido e está sendo validado e processado. Útil para atualizar o status da transação em tempo real.",
        category: "PIX Recebidos",
        categoryIcon: "ArrowDownCircle",
        useCases: [
          "Atualizar status de pedidos em tempo real",
          "Notificar clientes sobre pagamentos em processamento",
          "Iniciar processos de validação internos"
        ],
        payloadExample: {
          id: "tx_abc123",
          externalId: "ext_tx_abc123",
          referenceCode: "REF202312151030",
          endToEndId: "E12345678202312151234567890123456",
          status: "PROCESSING",
          type: "RECEIVE",
          amount: 10000,
          customerName: "João Silva",
          customerEmail: "<EMAIL>",
          customerDocument: "12345678901",
          createdAt: "2023-12-15T10:30:00Z",
          updatedAt: "2023-12-15T10:30:00Z",
          paymentAt: null,
          organizationId: "org_example123",
          pixKey: null,
          pixKeyType: null,
          description: "Pagamento PIX",
          percentFee: 0,
          fixedFee: 50,
          totalFee: 50,
          netAmount: 9950
        },
        frequency: "Imediato",
        reliability: "Alta"
      },
      {
        type: WebhookEventType.PIX_IN_CONFIRMATION,
        title: "PIX Recebido - Confirmado",
        description: "Acionado quando um PIX de entrada é confirmado e processado com sucesso",
        detailedDescription: "Este evento confirma que o PIX foi recebido, validado e processado com sucesso. O valor já está disponível na conta.",
        category: "PIX Recebidos",
        categoryIcon: "ArrowDownCircle",
        useCases: [
          "Confirmar pagamentos e liberar produtos/serviços",
          "Atualizar saldo de contas",
          "Enviar confirmações para clientes",
          "Integrar com sistemas de faturamento"
        ],
        payloadExample: {
          id: "tx_abc123",
          externalId: "ext_tx_abc123",
          referenceCode: "REF202312151030",
          endToEndId: "E12345678202312151234567890123456",
          status: "APPROVED",
          previousStatus: "PROCESSING",
          type: "RECEIVE",
          amount: 10000,
          customerName: "João Silva",
          customerEmail: "<EMAIL>",
          customerDocument: "12345678901",
          createdAt: "2023-12-15T10:30:00Z",
          updatedAt: "2023-12-15T10:30:15Z",
          paymentAt: "2023-12-15T10:30:15Z",
          organizationId: "org_example123",
          pixKey: null,
          pixKeyType: null,
          description: "Pagamento PIX",
          percentFee: 0,
          fixedFee: 50,
          totalFee: 50,
          netAmount: 9950
        },
        frequency: "Imediato",
        reliability: "Alta"
      },

      // PIX Outgoing Events
      {
        type: WebhookEventType.PIX_OUT_PROCESSING,
        title: "PIX Enviado - Processando",
        description: "Acionado quando um PIX de saída está sendo processado",
        detailedDescription: "Este evento é disparado quando uma transferência PIX é iniciada e está sendo processada pelo sistema bancário.",
        category: "PIX Enviados",
        categoryIcon: "ArrowUpCircle",
        useCases: [
          "Atualizar status de transferências",
          "Notificar sobre início de processamento",
          "Controlar fluxo de caixa em tempo real"
        ],
        payloadExample: {
          id: "tx_def456",
          externalId: "ext_tx_def456",
          referenceCode: "REF202312151100",
          endToEndId: "E12345678202312151234567890123457",
          status: "PROCESSING",
          type: "SEND",
          amount: 5000,
          customerName: "Maria Santos",
          customerEmail: "<EMAIL>",
          customerDocument: "98765432100",
          createdAt: "2023-12-15T11:00:00Z",
          updatedAt: "2023-12-15T11:00:00Z",
          paymentAt: null,
          organizationId: "org_example123",
          pixKey: "<EMAIL>",
          pixKeyType: "EMAIL",
          description: "Transferência PIX",
          percentFee: 0,
          fixedFee: 50,
          totalFee: 50,
          netAmount: 4950
        },
        frequency: "Imediato",
        reliability: "Alta"
      },
      {
        type: WebhookEventType.PIX_OUT_CONFIRMATION,
        title: "PIX Enviado - Confirmado",
        description: "Acionado quando um PIX de saída é confirmado com sucesso",
        detailedDescription: "Este evento confirma que a transferência PIX foi processada e concluída com sucesso pelo sistema bancário.",
        category: "PIX Enviados",
        categoryIcon: "ArrowUpCircle",
        useCases: [
          "Confirmar transferências realizadas",
          "Atualizar registros contábeis",
          "Notificar conclusão de pagamentos",
          "Reconciliação bancária"
        ],
        payloadExample: {
          id: "tx_def456",
          externalId: "ext_tx_def456",
          referenceCode: "REF202312151100",
          endToEndId: "E12345678202312151234567890123457",
          status: "APPROVED",
          previousStatus: "PROCESSING",
          type: "SEND",
          amount: 5000,
          customerName: "Maria Santos",
          customerEmail: "<EMAIL>",
          customerDocument: "98765432100",
          createdAt: "2023-12-15T11:00:00Z",
          updatedAt: "2023-12-15T11:00:30Z",
          paymentAt: "2023-12-15T11:00:30Z",
          organizationId: "org_example123",
          pixKey: "<EMAIL>",
          pixKeyType: "EMAIL",
          description: "Transferência PIX",
          percentFee: 0,
          fixedFee: 50,
          totalFee: 50,
          netAmount: 4950
        },
        frequency: "Imediato",
        reliability: "Alta"
      },
      {
        type: WebhookEventType.PIX_OUT_FAILURE,
        title: "PIX Enviado - Falha",
        description: "Acionado quando um PIX de saída falha no processamento",
        detailedDescription: "Este evento é disparado quando uma transferência PIX não pode ser concluída devido a erros como saldo insuficiente, chave PIX inválida, ou problemas técnicos.",
        category: "PIX Enviados",
        categoryIcon: "ArrowUpCircle",
        useCases: [
          "Notificar falhas em transferências",
          "Reverter operações pendentes",
          "Alertar sobre problemas técnicos",
          "Reprocessar transações falhadas"
        ],
        payloadExample: {
          id: "tx_ghi789",
          externalId: "ext_tx_ghi789",
          referenceCode: "REF202312151200",
          endToEndId: "E12345678202312151234567890123458",
          status: "REJECTED",
          previousStatus: "PROCESSING",
          type: "SEND",
          amount: 2000,
          customerName: "Destinatário Inválido",
          customerEmail: null,
          customerDocument: null,
          createdAt: "2023-12-15T12:00:00Z",
          updatedAt: "2023-12-15T12:00:05Z",
          paymentAt: null,
          organizationId: "org_example123",
          pixKey: "chave-invalida",
          pixKeyType: "RANDOM",
          description: "Transferência PIX falhada",
          percentFee: 0,
          fixedFee: 50,
          totalFee: 50,
          netAmount: 1950
        },
        frequency: "Conforme necessário",
        reliability: "Alta"
      },

      // PIX Reversal Events
      {
        type: WebhookEventType.PIX_IN_REVERSAL_PROCESSING,
        title: "Estorno PIX Recebido - Processando",
        description: "Acionado quando um estorno de PIX recebido está sendo processado",
        detailedDescription: "Este evento é disparado quando um estorno (devolução) de um PIX recebido é solicitado e está sendo processado.",
        category: "Estornos PIX",
        categoryIcon: "RotateCcw",
        useCases: [
          "Processar devoluções de pagamentos",
          "Atualizar status de estornos",
          "Notificar sobre início de reversão"
        ],
        payloadExample: {
          id: "tx_jkl012",
          externalId: "ext_tx_jkl012",
          referenceCode: "REF202312151300",
          endToEndId: "E12345678202312151234567890123458",
          status: "PROCESSING",
          type: "REFUND",
          amount: 7500,
          customerName: "João Silva",
          customerEmail: "<EMAIL>",
          customerDocument: "12345678901",
          createdAt: "2023-12-15T13:00:00Z",
          updatedAt: "2023-12-15T13:00:00Z",
          paymentAt: null,
          organizationId: "org_example123",
          pixKey: null,
          pixKeyType: null,
          description: "Estorno PIX recebido",
          percentFee: 0,
          fixedFee: 50,
          totalFee: 50,
          netAmount: 7450,
          originalTransactionId: "tx_abc123",
          reason: "Produto não entregue"
        },
        frequency: "Conforme necessário",
        reliability: "Alta"
      },
      {
        type: WebhookEventType.PIX_IN_REVERSAL_CONFIRMATION,
        title: "Estorno PIX Recebido - Confirmado",
        description: "Acionado quando um estorno de PIX recebido é confirmado",
        detailedDescription: "Este evento confirma que o estorno foi processado com sucesso e o valor foi devolvido ao pagador original.",
        category: "Estornos PIX",
        categoryIcon: "RotateCcw",
        useCases: [
          "Confirmar devoluções processadas",
          "Atualizar saldos após estorno",
          "Notificar conclusão de reversões",
          "Reconciliação de estornos"
        ],
        payloadExample: {
          id: "tx_jkl012",
          externalId: "ext_tx_jkl012",
          referenceCode: "REF202312151300",
          endToEndId: "E12345678202312151234567890123458",
          status: "APPROVED",
          previousStatus: "PROCESSING",
          type: "REFUND",
          amount: 7500,
          customerName: "João Silva",
          customerEmail: "<EMAIL>",
          customerDocument: "12345678901",
          createdAt: "2023-12-15T13:00:00Z",
          updatedAt: "2023-12-15T13:00:45Z",
          paymentAt: "2023-12-15T13:00:45Z",
          organizationId: "org_example123",
          pixKey: null,
          pixKeyType: null,
          description: "Estorno PIX recebido",
          percentFee: 0,
          fixedFee: 50,
          totalFee: 50,
          netAmount: 7450,
          originalTransactionId: "tx_abc123",
          reason: "Produto não entregue"
        },
        frequency: "Conforme necessário",
        reliability: "Alta"
      },
      {
        type: WebhookEventType.PIX_OUT_REVERSAL,
        title: "Estorno PIX Enviado",
        description: "Acionado quando um estorno de PIX enviado é processado",
        detailedDescription: "Este evento é disparado quando um PIX enviado é estornado (devolvido pelo recebedor) e o valor retorna para a conta de origem.",
        category: "Estornos PIX",
        categoryIcon: "RotateCcw",
        useCases: [
          "Processar devoluções recebidas",
          "Atualizar saldos com valores devolvidos",
          "Notificar sobre estornos recebidos",
          "Controle de fluxo de caixa"
        ],
        payloadExample: {
          id: "tx_mno345",
          externalId: "ext_tx_mno345",
          referenceCode: "REF202312151400",
          endToEndId: "E12345678202312151234567890123459",
          status: "APPROVED",
          type: "REFUND",
          amount: 3000,
          customerName: "Maria Santos",
          customerEmail: "<EMAIL>",
          customerDocument: "98765432100",
          createdAt: "2023-12-15T14:00:00Z",
          updatedAt: "2023-12-15T14:00:00Z",
          paymentAt: "2023-12-15T14:00:00Z",
          organizationId: "org_example123",
          pixKey: "<EMAIL>",
          pixKeyType: "EMAIL",
          description: "Estorno PIX enviado",
          percentFee: 0,
          fixedFee: 50,
          totalFee: 50,
          netAmount: 2950,
          originalTransactionId: "tx_def456",
          reason: "Devolução solicitada pelo recebedor"
        },
        frequency: "Conforme necessário",
        reliability: "Alta"
      }
    ];

    return NextResponse.json({
      data: eventTypeDescriptions,
    });
  } catch (error) {
    console.error("Error getting webhook event types", error);
    return NextResponse.json(
      { error: "Failed to get webhook event types" },
      { status: 500 }
    );
  }
}
