"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@ui/components/sheet";
import { ArrowDownLeft, ArrowUpRight, Copy } from "lucide-react";
import { cn } from "@ui/lib";
import { badge } from "@ui/components/badge";
import { toast } from "sonner";
import { Button } from "@ui/components/button";
import { format } from "date-fns";

// Interface para transações admin
interface AdminTransaction {
  id: string;
  externalId: string | null;
  referenceCode: string | null;
  customerName: string;
  customerEmail: string;
  amount: number;
  status: string;
  type: string;
  createdAt: string;
  paymentAt: string | null;
  organizationId: string;
  organizationName: string;
  organizationSlug: string;
  gateway?: {
    name: string;
    type: string;
  } | null;
  description?: string;
  fee?: number | string;
  platformFee?: number | string;
  netAmount?: number | string;
  // Dedicated fee fields
  percentFee?: number;
  fixedFee?: number;
  totalFee?: number;
  customerPhone?: string;
  customerDocument?: string;
  customerDocumentType?: string;
  // PIX specific identifiers
  endToEndId?: string | null;
  pixEndToEndId?: string | null;
  metadata?: Record<string, any> | null;
}

type AdminTransactionDetailsSheetProps = {
  isOpen: boolean;
  onClose: () => void;
  transaction: AdminTransaction | null;
};

export function AdminTransactionDetailsSheet({
  isOpen,
  onClose,
  transaction
}: AdminTransactionDetailsSheetProps) {
  // Se não houver transação ou não estiver aberto, não renderize nada
  if (!transaction || !isOpen) return null;

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Copiado para a área de transferência");
  };

  // Função para obter o ícone do tipo de transação
  const getTransactionTypeIcon = () => {
    const isIncoming = transaction.type === "RECEIVE";
    return isIncoming ? (
      <div className="p-2 bg-success/10 dark:bg-success/20 rounded-full text-success dark:text-primary">
        <ArrowDownLeft size={18} />
      </div>
    ) : (
      <div className="p-2 bg-destructive/10 dark:bg-destructive/20 rounded-full text-destructive dark:text-destructive">
        <ArrowUpRight size={18} />
      </div>
    );
  };

  // Formatar data
  const formatDate = (dateString?: string | null) => {
    if (!dateString) return "-";
    try {
      const date = new Date(dateString);
      return format(date, "dd/MM/yyyy HH:mm:ss");
    } catch (e) {
      return dateString;
    }
  };

  // Formatar valor
  const formatCurrency = (value?: number | string) => {
    if (value === undefined) return "-";

    const numValue = typeof value === 'string' ? parseFloat(value) : value;

    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(numValue);
  };

  // Mapear status para exibição
  const getStatusBadge = () => {
    switch (transaction.status) {
      case "APPROVED":
        return (
          <span className={cn(badge({ status: "success" }))}>
            Aprovado
          </span>
        );
      case "REJECTED":
      case "CANCELED":
      case "BLOCKED":
        return (
          <span className={cn(badge({ status: "error" }))}>
            Rejeitado
          </span>
        );
      case "REFUNDED":
        return (
          <span className={cn(badge())}>
            Estornado
          </span>
        );
      case "PROCESSING":
      case "PENDING":
      default:
        return (
          <span className={cn(badge({ status: "warning" }))}>
            Pendente
          </span>
        );
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full sm:max-w-md md:max-w-lg lg:max-w-xl p-0 border-l border-gray-800 bg-gray-900/80 backdrop-blur-sm">
        <div className="flex flex-col h-full">
          {/* Cabeçalho */}
          <div className="p-4 sm:p-5 border-b border-gray-800 flex items-center justify-between">
            <SheetTitle className="text-xl font-semibold">
              Detalhes da Transação
            </SheetTitle>
          </div>

          {/* Conteúdo */}
          <div className="flex-1 overflow-auto">
            <div className="p-4 sm:p-5 space-y-5">
              {/* ID da Transação e Status */}
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  {getTransactionTypeIcon()}
                  <div>
                    <div className="text-sm text-gray-400">Transação</div>
                    <div className="font-medium flex items-center gap-1 max-w-[180px] truncate"
                         title={transaction.referenceCode || transaction.id}>
                      {transaction.referenceCode || transaction.id}
                      <button
                        onClick={() => copyToClipboard(transaction.referenceCode || transaction.id)}
                        className="text-gray-500 hover:text-white flex-shrink-0"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                </div>
                <div>
                  {getStatusBadge()}
                </div>
              </div>

              {/* Organização */}
              <div className="p-4 bg-gray-800/30 rounded-lg">
                <div className="text-sm text-gray-400 mb-1">Organização</div>
                <div className="font-medium">{transaction.organizationName}</div>
                <div className="text-xs text-gray-400">{transaction.organizationId}</div>
              </div>

              {/* Datas */}
              <div className="space-y-3 bg-gray-800/30 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <div className="text-sm font-medium text-gray-400">Criação</div>
                  <div className="text-sm">{formatDate(transaction.createdAt)}</div>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm font-medium text-gray-400">Pagamento</div>
                  <div className="text-sm">{formatDate(transaction.paymentAt)}</div>
                </div>
              </div>

              {/* Cliente */}
              <div className="space-y-3 bg-gray-800/30 rounded-lg p-4">
                <div className="text-sm font-medium">Dados do Cliente</div>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-400">Nome</div>
                  <div className="text-sm">{transaction.customerName}</div>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-400">E-mail</div>
                  <div className="text-sm">{transaction.customerEmail}</div>
                </div>
                {transaction.customerPhone && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-400">Telefone</div>
                    <div className="text-sm">{transaction.customerPhone}</div>
                  </div>
                )}
                {transaction.customerDocument && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-400">
                      {transaction.customerDocumentType || "Documento"}
                    </div>
                    <div className="text-sm">{transaction.customerDocument}</div>
                  </div>
                )}
              </div>

              {/* Dados da Transação */}
              <div className="bg-gray-800/30 p-4 rounded-lg space-y-3">
                <h3 className="font-medium">Dados Financeiros</h3>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-400">Valor</div>
                  <div className="text-sm font-medium">{formatCurrency(transaction.amount)}</div>
                </div>
                {/* Display transaction fees */}
                {(transaction.totalFee && transaction.totalFee > 0) || transaction.fee ? (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-400">Taxa por Transação</div>
                    <div className="text-sm font-medium text-red-500">
                      -{transaction.totalFee && transaction.totalFee > 0 ? formatCurrency(transaction.totalFee) : formatCurrency(transaction.fee)}
                    </div>
                  </div>
                ) : null}
                {transaction.fixedFee && transaction.fixedFee > 0 && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-400">Taxa Fixa</div>
                    <div className="text-sm font-medium text-red-500">-{formatCurrency(transaction.fixedFee)}</div>
                  </div>
                )}
                {transaction.percentFee && transaction.percentFee > 0 && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-400">Taxa Percentual</div>
                    <div className="text-sm font-medium text-red-500">-{formatCurrency(transaction.percentFee)}</div>
                  </div>
                )}
                {/* PIX End-to-End ID */}
                {transaction.endToEndId && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-400">PIX End-to-End ID</div>
                    <div className="text-sm flex items-center gap-1">
                      <span className="max-w-[180px] truncate">
                        {transaction.endToEndId}
                      </span>
                      <button
                        onClick={() => copyToClipboard(transaction.endToEndId || "")}
                        className="text-gray-500 hover:text-white"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                )}
                {transaction.platformFee && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-400">Taxa da Plataforma</div>
                    <div className="text-sm font-medium text-red-500">-{formatCurrency(transaction.platformFee)}</div>
                  </div>
                )}
                {transaction.netAmount && (
                  <>
                    <hr className="border-gray-700" />
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-semibold">Valor Líquido</div>
                      <div className="text-sm font-semibold text-green-500">{formatCurrency(transaction.netAmount)}</div>
                    </div>
                  </>
                )}
              </div>

              {/* Gateway */}
              {transaction.gateway && (
                <div className="bg-gray-800/30 p-4 rounded-lg space-y-3">
                  <h3 className="font-medium">Gateway</h3>
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-400">Nome</div>
                    <div className="text-sm">{transaction.gateway.name}</div>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-400">Tipo</div>
                    <div className="text-sm">{transaction.gateway.type}</div>
                  </div>
                </div>
              )}

              {/* Botões de ação */}
              <div className="flex justify-end space-x-2 mt-6">
                <Button variant="outline" onClick={onClose}>
                  Fechar
                </Button>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
