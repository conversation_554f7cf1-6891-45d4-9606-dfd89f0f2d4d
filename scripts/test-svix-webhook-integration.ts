#!/usr/bin/env tsx

/**
 * SVIX Webhook Integration Test Script
 * 
 * This script tests the SVIX webhook integration to ensure:
 * 1. Test events are sent to correct organization-specific channels
 * 2. Event payloads match production structure
 * 3. SVIX event type mapping is correct
 * 4. Organization channels are properly configured
 */

import { WebhookEventType } from "../packages/payments/src/webhooks/events";
import { EVENT_TYPE_MAPPING } from "../packages/utils/src/svix/index";

const BASE_URL = "http://localhost:3000";
const TEST_ORGANIZATIONS = [
  "cm123test456",
  "cm789test012"
];

interface SvixTestResult {
  eventType: string;
  organizationId: string;
  success: boolean;
  messageId?: string;
  svixEventType?: string;
  channel?: string;
  payload?: Record<string, any>;
  error?: string;
  responseTime?: number;
}

class SvixWebhookTester {
  private results: SvixTestResult[] = [];

  async run(): Promise<void> {
    console.log("🚀 Starting SVIX Webhook Integration Test");
    console.log("=" .repeat(60));

    try {
      // Test each PIX event type for each organization
      for (const orgId of TEST_ORGANIZATIONS) {
        console.log(`\n🏢 Testing organization: ${orgId}`);
        await this.testOrganizationWebhooks(orgId);
      }

      // Generate comprehensive report
      this.generateReport();

    } catch (error) {
      console.error("❌ SVIX integration test failed:", error);
      process.exit(1);
    }
  }

  private async testOrganizationWebhooks(organizationId: string): Promise<void> {
    const pixEvents = [
      WebhookEventType.PIX_IN_PROCESSING,
      WebhookEventType.PIX_IN_CONFIRMATION,
      WebhookEventType.PIX_OUT_PROCESSING,
      WebhookEventType.PIX_OUT_CONFIRMATION,
      WebhookEventType.PIX_OUT_FAILURE,
      WebhookEventType.PIX_IN_REVERSAL_PROCESSING,
      WebhookEventType.PIX_IN_REVERSAL_CONFIRMATION,
      WebhookEventType.PIX_OUT_REVERSAL,
    ];

    for (const eventType of pixEvents) {
      await this.testSvixWebhookEvent(eventType, organizationId);
    }
  }

  private async testSvixWebhookEvent(
    eventType: string, 
    organizationId: string
  ): Promise<void> {
    console.log(`  🧪 Testing ${eventType}...`);
    
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${BASE_URL}/api/webhooks/test`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          eventType,
          organizationId,
        }),
      });

      const responseTime = Date.now() - startTime;

      if (!response.ok) {
        const errorText = await response.text();
        this.results.push({
          eventType,
          organizationId,
          success: false,
          error: `HTTP ${response.status}: ${errorText}`,
          responseTime,
        });
        console.log(`    ❌ Failed (${responseTime}ms)`);
        return;
      }

      const data = await response.json();
      
      // Verify SVIX-specific response data
      const result: SvixTestResult = {
        eventType,
        organizationId,
        success: true,
        messageId: data.data?.messageId,
        svixEventType: data.data?.svixEventType,
        channel: data.data?.channel,
        payload: data.data?.payload,
        responseTime,
      };

      this.results.push(result);
      
      // Verify channel format
      const expectedChannel = `org-${organizationId}`;
      if (result.channel !== expectedChannel) {
        console.log(`    ⚠️  Channel mismatch: expected ${expectedChannel}, got ${result.channel}`);
      }
      
      // Verify SVIX event type mapping
      const expectedSvixType = EVENT_TYPE_MAPPING[eventType as keyof typeof EVENT_TYPE_MAPPING];
      if (result.svixEventType !== expectedSvixType) {
        console.log(`    ⚠️  SVIX type mismatch: expected ${expectedSvixType}, got ${result.svixEventType}`);
      }
      
      console.log(`    ✅ Success (${responseTime}ms) - ${result.messageId?.substring(0, 8)}...`);

    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.results.push({
        eventType,
        organizationId,
        success: false,
        error: error instanceof Error ? error.message : String(error),
        responseTime,
      });
      console.log(`    ❌ Error (${responseTime}ms): ${error}`);
    }
  }

  private generateReport(): void {
    console.log("\n" + "=".repeat(60));
    console.log("📊 SVIX WEBHOOK INTEGRATION TEST REPORT");
    console.log("=".repeat(60));
    
    const totalTests = this.results.length;
    const successfulTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - successfulTests;
    
    const avgResponseTime = this.results
      .filter(r => r.responseTime)
      .reduce((sum, r) => sum + (r.responseTime || 0), 0) / totalTests;
    
    console.log(`\n📈 Overall Results:`);
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Successful: ${successfulTests} (${((successfulTests/totalTests)*100).toFixed(1)}%)`);
    console.log(`   Failed: ${failedTests}`);
    console.log(`   Avg Response Time: ${avgResponseTime.toFixed(0)}ms`);
    
    // Organization-specific results
    console.log(`\n🏢 Results by Organization:`);
    for (const orgId of TEST_ORGANIZATIONS) {
      const orgResults = this.results.filter(r => r.organizationId === orgId);
      const orgSuccess = orgResults.filter(r => r.success).length;
      console.log(`   ${orgId}: ${orgSuccess}/${orgResults.length} successful`);
    }
    
    // Event type results
    console.log(`\n🎯 Results by Event Type:`);
    const eventTypes = [...new Set(this.results.map(r => r.eventType))];
    for (const eventType of eventTypes) {
      const eventResults = this.results.filter(r => r.eventType === eventType);
      const eventSuccess = eventResults.filter(r => r.success).length;
      console.log(`   ${eventType}: ${eventSuccess}/${eventResults.length} successful`);
    }
    
    // SVIX-specific verification
    console.log(`\n🔧 SVIX Integration Verification:`);
    const successfulResults = this.results.filter(r => r.success);
    
    // Check channel consistency
    const channelIssues = successfulResults.filter(r => {
      const expectedChannel = `org-${r.organizationId}`;
      return r.channel !== expectedChannel;
    });
    
    if (channelIssues.length === 0) {
      console.log(`   ✅ All events sent to correct organization channels`);
    } else {
      console.log(`   ⚠️  ${channelIssues.length} events sent to incorrect channels`);
    }
    
    // Check SVIX event type mapping
    const mappingIssues = successfulResults.filter(r => {
      const expectedSvixType = EVENT_TYPE_MAPPING[r.eventType as keyof typeof EVENT_TYPE_MAPPING];
      return r.svixEventType !== expectedSvixType;
    });
    
    if (mappingIssues.length === 0) {
      console.log(`   ✅ All events use correct SVIX event type mapping`);
    } else {
      console.log(`   ⚠️  ${mappingIssues.length} events have incorrect SVIX type mapping`);
    }
    
    // Check message IDs
    const messageIds = successfulResults.map(r => r.messageId).filter(Boolean);
    const uniqueMessageIds = new Set(messageIds);
    
    if (messageIds.length === uniqueMessageIds.size) {
      console.log(`   ✅ All events have unique message IDs`);
    } else {
      console.log(`   ⚠️  Duplicate message IDs detected`);
    }
    
    // Performance analysis
    console.log(`\n⚡ Performance Analysis:`);
    const responseTimes = this.results
      .filter(r => r.responseTime)
      .map(r => r.responseTime!)
      .sort((a, b) => a - b);
    
    if (responseTimes.length > 0) {
      const p50 = responseTimes[Math.floor(responseTimes.length * 0.5)];
      const p95 = responseTimes[Math.floor(responseTimes.length * 0.95)];
      const max = responseTimes[responseTimes.length - 1];
      
      console.log(`   P50 Response Time: ${p50}ms`);
      console.log(`   P95 Response Time: ${p95}ms`);
      console.log(`   Max Response Time: ${max}ms`);
      
      if (p95 < 2000) {
        console.log(`   ✅ Response times meet requirement (<2s)`);
      } else {
        console.log(`   ⚠️  Response times exceed requirement (>2s)`);
      }
    }
    
    // Failed tests details
    if (failedTests > 0) {
      console.log(`\n❌ Failed Tests Details:`);
      const failed = this.results.filter(r => !r.success);
      for (const failure of failed) {
        console.log(`   ${failure.eventType} (${failure.organizationId}): ${failure.error}`);
      }
    }
    
    // Summary
    console.log("\n" + "=".repeat(60));
    console.log("🎯 SUMMARY");
    console.log("=".repeat(60));
    
    if (successfulTests === totalTests) {
      console.log("🎉 EXCELLENT: All SVIX webhook tests passed!");
      console.log("✅ Organization-specific channels working correctly");
      console.log("✅ Event type mapping is consistent");
      console.log("✅ Message IDs are unique");
    } else if (successfulTests >= totalTests * 0.9) {
      console.log("✅ GOOD: Most SVIX webhook tests passed");
      console.log("⚠️  Some issues detected - review failed tests");
    } else {
      console.log("⚠️  NEEDS ATTENTION: Multiple SVIX webhook failures");
      console.log("❌ Review SVIX configuration and integration");
    }
    
    console.log("\n✨ SVIX integration test completed!");
  }
}

// Run the test
const tester = new SvixWebhookTester();
tester.run().catch(console.error);
