#!/usr/bin/env tsx

import { db } from "../packages/database";
import { triggerTransactionEvents, WebhookEventType } from "../packages/payments/src/webhooks/events";
import { EVENT_TYPE_MAPPING } from "../packages/utils/src/svix";

async function main() {
  try {
    console.log("🧪 Testing Webhook Events Alignment...\n");

    // Test 1: Verify all promised events from configuration page are implemented
    console.log("📋 Test 1: Checking promised vs implemented events");
    
    const promisedEvents = [
      WebhookEventType.PIX_IN_PROCESSING,
      WebhookEventType.PIX_IN_CONFIRMATION,
      WebhookEventType.PIX_OUT_PROCESSING,
      WebhookEventType.PIX_OUT_CONFIRMATION,
      WebhookEventType.PIX_OUT_FAILURE,
      WebhookEventType.PIX_IN_REVERSAL_PROCESSING,
      WebhookEventType.PIX_IN_REVERSAL_CONFIRMATION,
      WebhookEventType.PIX_OUT_REVERSAL,
    ];

    console.log("✅ Promised PIX events:");
    promisedEvents.forEach(event => {
      console.log(`   - ${event}`);
    });

    // Test 2: Verify SVIX event mapping consistency
    console.log("\n📋 Test 2: Checking SVIX event mapping consistency");
    
    const mappingIssues: string[] = [];
    
    Object.entries(EVENT_TYPE_MAPPING).forEach(([internal, svix]) => {
      // Check for inconsistent naming patterns
      if (svix.includes("External") && !internal.includes("external")) {
        mappingIssues.push(`❌ Inconsistent naming: ${internal} → ${svix} (contains "External")`);
      }
      
      // Check for consistent dot notation
      if (internal.includes(".") && !svix.includes(".") && !svix.match(/[A-Z][a-z]+[A-Z]/)) {
        mappingIssues.push(`⚠️ Potential naming issue: ${internal} → ${svix}`);
      }
    });

    if (mappingIssues.length === 0) {
      console.log("✅ SVIX event mapping is consistent");
    } else {
      console.log("❌ SVIX event mapping issues found:");
      mappingIssues.forEach(issue => console.log(`   ${issue}`));
    }

    // Test 3: Create test transactions and verify correct events are triggered
    console.log("\n📋 Test 3: Testing PIX transaction event triggering");

    // Find an existing organization for testing
    const testOrg = await db.organization.findFirst({
      where: { status: "APPROVED" }
    });

    if (!testOrg) {
      console.log("⚠️ No approved organization found for testing");
      return;
    }

    console.log(`Using test organization: ${testOrg.id}`);

    // Test PIX charge creation (should trigger pix.in.processing)
    console.log("\n🔍 Testing PIX charge creation...");
    
    const pixCharge = await db.transaction.create({
      data: {
        amount: 10.00,
        status: "PENDING",
        type: "CHARGE",
        customerName: "Test Customer",
        customerEmail: "<EMAIL>",
        description: "Test PIX charge",
        organizationId: testOrg.id,
        pixKey: "<EMAIL>",
        pixKeyType: "EMAIL",
        referenceCode: `test_charge_${Date.now()}`,
      }
    });

    // Trigger events and check what was created
    await triggerTransactionEvents(pixCharge);

    const chargeEvents = await db.webhookEvent.findMany({
      where: { transactionId: pixCharge.id },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`📊 Events created for PIX charge:`);
    chargeEvents.forEach(event => {
      console.log(`   - ${event.type} (${event.svixEventType || 'no SVIX mapping'})`);
    });

    // Verify correct event was sent
    const hasPixInProcessing = chargeEvents.some(e => e.type === WebhookEventType.PIX_IN_PROCESSING);
    const hasGenericCreated = chargeEvents.some(e => e.type === WebhookEventType.TRANSACTION_CREATED);

    if (hasPixInProcessing && !hasGenericCreated) {
      console.log("✅ PIX charge correctly triggered pix.in.processing (not transaction.created)");
    } else if (hasGenericCreated && !hasPixInProcessing) {
      console.log("❌ PIX charge incorrectly triggered transaction.created (should be pix.in.processing)");
    } else {
      console.log("⚠️ PIX charge triggered unexpected events");
    }

    // Test PIX transfer creation (should trigger pix.out.processing)
    console.log("\n🔍 Testing PIX transfer creation...");
    
    const pixTransfer = await db.transaction.create({
      data: {
        amount: 5.00,
        status: "PENDING",
        type: "SEND",
        customerName: "Transfer Recipient",
        customerEmail: "<EMAIL>",
        description: "Test PIX transfer",
        organizationId: testOrg.id,
        pixKey: "<EMAIL>",
        pixKeyType: "EMAIL",
        referenceCode: `test_transfer_${Date.now()}`,
      }
    });

    await triggerTransactionEvents(pixTransfer);

    const transferEvents = await db.webhookEvent.findMany({
      where: { transactionId: pixTransfer.id },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`📊 Events created for PIX transfer:`);
    transferEvents.forEach(event => {
      console.log(`   - ${event.type} (${event.svixEventType || 'no SVIX mapping'})`);
    });

    const hasPixOutProcessing = transferEvents.some(e => e.type === WebhookEventType.PIX_OUT_PROCESSING);
    const hasGenericCreatedTransfer = transferEvents.some(e => e.type === WebhookEventType.TRANSACTION_CREATED);

    if (hasPixOutProcessing && !hasGenericCreatedTransfer) {
      console.log("✅ PIX transfer correctly triggered pix.out.processing (not transaction.created)");
    } else if (hasGenericCreatedTransfer && !hasPixOutProcessing) {
      console.log("❌ PIX transfer incorrectly triggered transaction.created (should be pix.out.processing)");
    } else {
      console.log("⚠️ PIX transfer triggered unexpected events");
    }

    // Test PIX transfer failure (should trigger pix.out.failure)
    console.log("\n🔍 Testing PIX transfer failure...");
    
    // Update transfer to REJECTED status
    const rejectedTransfer = await db.transaction.update({
      where: { id: pixTransfer.id },
      data: { status: "REJECTED" }
    });

    await triggerTransactionEvents(rejectedTransfer, "PENDING");

    const failureEvents = await db.webhookEvent.findMany({
      where: { 
        transactionId: pixTransfer.id,
        type: WebhookEventType.PIX_OUT_FAILURE
      }
    });

    if (failureEvents.length > 0) {
      console.log("✅ PIX transfer failure correctly triggered pix.out.failure");
    } else {
      console.log("❌ PIX transfer failure did not trigger pix.out.failure");
    }

    // Cleanup test transactions
    await db.webhookEvent.deleteMany({
      where: {
        transactionId: { in: [pixCharge.id, pixTransfer.id] }
      }
    });
    
    await db.transaction.deleteMany({
      where: {
        id: { in: [pixCharge.id, pixTransfer.id] }
      }
    });

    console.log("\n🧹 Cleaned up test data");

    console.log("\n✅ Webhook events alignment test completed!");

  } catch (error) {
    console.error("❌ Error during webhook events alignment test:", error);
  } finally {
    await db.$disconnect();
  }
}

main();
