#!/usr/bin/env tsx

/**
 * Test script to verify PIX charge fee calculation and storage
 * 
 * This script tests the fee calculation and storage for PIX charge transactions
 * to ensure that fees are properly calculated and stored in both metadata and
 * dedicated database fields.
 */

import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { processApprovedTransactionFees } from "@repo/payments/src/taxes/fee-service";
import { calculateTransactionFees } from "@repo/payments/src/taxes/calculator";

async function main() {
  console.log("🧪 Testing PIX charge fee calculation and storage...\n");

  try {
    // Find a test organization
    const organization = await db.organization.findFirst({
      include: {
        taxes: true
      }
    });

    if (!organization) {
      console.log("❌ No organization found for testing");
      return;
    }

    console.log(`📋 Using organization: ${organization.name} (${organization.id})`);

    // Check organization taxes configuration
    if (!organization.taxes) {
      console.log("⚠️  Organization has no tax configuration, creating default...");
      await db.organizationTaxes.create({
        data: {
          organizationId: organization.id,
          pixChargeFixedFee: 10, // 10 cents fee for testing
          pixChargePercentFee: 0,
          pixTransferFixedFee: 5,
          pixTransferPercentFee: 1.5
        }
      });
      console.log("✅ Created default tax configuration");
    } else {
      console.log(`💰 Tax configuration found:`);
      console.log(`   PIX Charge Fixed Fee: R$ ${(organization.taxes.pixChargeFixedFee / 100).toFixed(2)}`);
      console.log(`   PIX Charge Percent Fee: ${organization.taxes.pixChargePercentFee}%`);
    }

    // Test fee calculation
    const testAmount = 0.50; // 50 centavos
    console.log(`\n🧮 Testing fee calculation for amount: R$ ${testAmount.toFixed(2)}`);

    const calculatedFees = await calculateTransactionFees(
      organization.id,
      testAmount,
      'CHARGE'
    );

    console.log(`📊 Calculated fees:`);
    console.log(`   Fixed Fee: R$ ${calculatedFees.fixedFee.toFixed(2)}`);
    console.log(`   Percent Fee: R$ ${calculatedFees.percentFee.toFixed(2)}`);
    console.log(`   Total Fee: R$ ${calculatedFees.totalFee.toFixed(2)}`);
    console.log(`   Source: ${calculatedFees.source}`);

    // Create a test transaction
    console.log(`\n🔄 Creating test PIX charge transaction...`);
    
    const testTransaction = await db.transaction.create({
      data: {
        amount: testAmount,
        status: "APPROVED",
        type: "CHARGE",
        customerName: "Test Customer",
        customerEmail: "<EMAIL>",
        organizationId: organization.id,
        description: "Test PIX charge for fee verification",
        referenceCode: `TEST_${Date.now()}`,
        // Initially no fees stored
        percentFee: 0,
        fixedFee: 0,
        totalFee: 0,
        netAmount: null
      }
    });

    console.log(`✅ Created test transaction: ${testTransaction.id}`);

    // Process fees using the service
    console.log(`\n⚙️  Processing fees using processApprovedTransactionFees...`);
    
    const result = await processApprovedTransactionFees(testTransaction);

    console.log(`📈 Fee processing result:`);
    console.log(`   Success: ${result.success}`);
    console.log(`   Calculated Fees: R$ ${result.fees.totalFee.toFixed(2)}`);
    console.log(`   Net Amount: R$ ${result.netAmount.toFixed(2)}`);

    // Verify the transaction was updated correctly
    const updatedTransaction = await db.transaction.findUnique({
      where: { id: testTransaction.id }
    });

    if (!updatedTransaction) {
      console.log("❌ Could not find updated transaction");
      return;
    }

    console.log(`\n🔍 Verifying updated transaction fields:`);
    console.log(`   percentFee: R$ ${updatedTransaction.percentFee.toFixed(2)}`);
    console.log(`   fixedFee: R$ ${updatedTransaction.fixedFee.toFixed(2)}`);
    console.log(`   totalFee: R$ ${updatedTransaction.totalFee.toFixed(2)}`);
    console.log(`   netAmount: R$ ${(updatedTransaction.netAmount || 0).toFixed(2)}`);

    // Check metadata
    const metadata = updatedTransaction.metadata as any;
    if (metadata?.fees) {
      console.log(`\n📝 Metadata fees:`);
      console.log(`   percentFee: R$ ${metadata.fees.percentFee.toFixed(2)}`);
      console.log(`   fixedFee: R$ ${metadata.fees.fixedFee.toFixed(2)}`);
      console.log(`   totalFee: R$ ${metadata.fees.totalFee.toFixed(2)}`);
      console.log(`   source: ${metadata.fees.source}`);
      console.log(`   calculatedAt: ${metadata.fees.calculatedAt}`);
    }

    // Verify webhook payload would be correct
    console.log(`\n🔗 Webhook payload verification:`);
    const webhookPayload = {
      id: updatedTransaction.id,
      amount: updatedTransaction.amount,
      percentFee: updatedTransaction.percentFee,
      fixedFee: updatedTransaction.fixedFee,
      totalFee: updatedTransaction.totalFee,
      netAmount: updatedTransaction.netAmount,
    };
    console.log(`   Webhook would show: ${JSON.stringify(webhookPayload, null, 2)}`);

    // Cleanup
    console.log(`\n🧹 Cleaning up test transaction...`);
    await db.transaction.delete({
      where: { id: testTransaction.id }
    });

    console.log(`\n✅ Test completed successfully!`);
    console.log(`\n📋 Summary:`);
    console.log(`   - Fee calculation: ✅ Working`);
    console.log(`   - Database field storage: ✅ Working`);
    console.log(`   - Metadata storage: ✅ Working`);
    console.log(`   - Webhook payload: ✅ Will show correct fees`);

  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
}

main()
  .then(() => {
    console.log("\n🎉 All tests passed!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Fatal error:", error);
    process.exit(1);
  });
