#!/usr/bin/env tsx

import { db } from "../packages/database";
import { calculateTransactionFees } from "../packages/payments/src/taxes/calculator";

async function main() {
  try {
    console.log("🔍 Investigando problema de cálculo de taxa no webhook...\n");

    // ID da transação do webhook
    const transactionId = "cmb2opfdc0001k00452zh3x6k";

    // Buscar a transação
    const transaction = await db.transaction.findUnique({
      where: { id: transactionId },
      include: {
        organization: {
          include: {
            taxes: true
          }
        }
      }
    });

    if (!transaction) {
      console.log("❌ Transação não encontrada!");
      return;
    }

    console.log("📊 Dados da transação:");
    console.log(`- ID: ${transaction.id}`);
    console.log(`- Tipo: ${transaction.type}`);
    console.log(`- Status: ${transaction.status}`);
    console.log(`- Valor: R$ ${transaction.amount.toFixed(2)}`);
    console.log(`- Taxa percentual: ${transaction.percentFee}`);
    console.log(`- Taxa fixa: ${transaction.fixedFee}`);
    console.log(`- Taxa total: ${transaction.totalFee}`);
    console.log(`- Valor líquido: ${transaction.netAmount || 'N/A'}`);
    console.log(`- Organização: ${transaction.organizationId}`);

    // Verificar as taxas da organização
    const orgTaxes = transaction.organization?.taxes;
    if (orgTaxes) {
      console.log("\n💰 Taxas configuradas na organização:");
      console.log(`- Taxa fixa PIX Transfer: R$ ${orgTaxes.pixTransferFixedFee.toFixed(2)}`);
      console.log(`- Taxa percentual PIX Transfer: ${orgTaxes.pixTransferPercentFee}%`);
      console.log(`- Taxa fixa PIX Charge: R$ ${orgTaxes.pixChargeFixedFee.toFixed(2)}`);
      console.log(`- Taxa percentual PIX Charge: ${orgTaxes.pixChargePercentFee}%`);
    } else {
      console.log("\n❌ Nenhuma configuração de taxa encontrada para a organização");
    }

    // Calcular as taxas que deveriam ser aplicadas
    console.log("\n🧮 Calculando taxas esperadas...");
    const expectedFees = await calculateTransactionFees(
      transaction.organizationId,
      transaction.amount,
      transaction.type === 'CHARGE' ? 'CHARGE' : 'TRANSFER',
      transaction.gatewayId || undefined
    );

    console.log("📋 Taxas esperadas:");
    console.log(`- Taxa percentual: R$ ${expectedFees.percentFee.toFixed(2)}`);
    console.log(`- Taxa fixa: R$ ${expectedFees.fixedFee.toFixed(2)}`);
    console.log(`- Taxa total: R$ ${expectedFees.totalFee.toFixed(2)}`);
    console.log(`- Fonte: ${expectedFees.source}`);

    // Comparar com os valores armazenados
    console.log("\n🔍 Comparação:");
    const percentFeeDiff = Math.abs(transaction.percentFee - expectedFees.percentFee);
    const fixedFeeDiff = Math.abs(transaction.fixedFee - expectedFees.fixedFee);
    const totalFeeDiff = Math.abs(transaction.totalFee - expectedFees.totalFee);

    console.log(`- Diferença taxa percentual: R$ ${percentFeeDiff.toFixed(3)}`);
    console.log(`- Diferença taxa fixa: R$ ${fixedFeeDiff.toFixed(3)}`);
    console.log(`- Diferença taxa total: R$ ${totalFeeDiff.toFixed(3)}`);

    if (totalFeeDiff > 0.001) {
      console.log("\n⚠️ PROBLEMA DETECTADO: Há diferença significativa nas taxas!");

      // Verificar se o problema está na conversão de centavos/reais
      console.log("\n🔄 Verificando possível problema de conversão:");
      console.log(`- Taxa armazenada: ${transaction.totalFee}`);
      console.log(`- Taxa esperada: ${expectedFees.totalFee}`);
      console.log(`- Taxa armazenada * 100: ${transaction.totalFee * 100}`);
      console.log(`- Taxa esperada / 100: ${expectedFees.totalFee / 100}`);

      // Verificar se a taxa armazenada está em centavos quando deveria estar em reais
      if (Math.abs(transaction.totalFee * 100 - expectedFees.totalFee) < 0.001) {
        console.log("🎯 CAUSA ENCONTRADA: Taxa armazenada está em reais, mas deveria estar em centavos!");
      } else if (Math.abs(transaction.totalFee - expectedFees.totalFee / 100) < 0.001) {
        console.log("🎯 CAUSA ENCONTRADA: Taxa armazenada está em centavos, mas deveria estar em reais!");
      }
    } else {
      console.log("\n✅ Taxas estão corretas!");
    }

    // Verificar metadados
    const metadata = transaction.metadata as any;
    if (metadata) {
      console.log("\n📝 Metadados da transação:");
      if (metadata.feeCalculation) {
        console.log("- Cálculo de taxa nos metadados:");
        console.log(`  - Taxa percentual: ${metadata.feeCalculation.percentFee}`);
        console.log(`  - Taxa fixa: ${metadata.feeCalculation.fixedFee}`);
        console.log(`  - Taxa total: ${metadata.feeCalculation.totalFee}`);
        console.log(`  - Fonte: ${metadata.feeCalculation.source}`);
      }
      if (metadata.fees) {
        console.log("- Taxas nos metadados:");
        console.log(`  - Taxa percentual: ${metadata.fees.percentFee}`);
        console.log(`  - Taxa fixa: ${metadata.fees.fixedFee}`);
        console.log(`  - Taxa total: ${metadata.fees.totalFee}`);
      }
    }

  } catch (error) {
    console.error("❌ Erro durante investigação:", error);
  } finally {
    await db.$disconnect();
  }
}

main();
