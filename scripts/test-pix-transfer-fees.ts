#!/usr/bin/env tsx

/**
 * Test script to verify PIX transfer fee calculation
 * This script tests the fee calculation system to ensure it's working correctly
 */

import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { calculateTransactionFees } from "@repo/payments/src/taxes/calculator";

async function main() {
  console.log("🧪 Testing PIX Transfer Fee Calculation System");
  console.log("=" .repeat(60));

  try {
    // Find a test organization
    const organization = await db.organization.findFirst({
      include: {
        taxes: true,
        gateways: {
          include: {
            gateway: true
          }
        }
      }
    });

    if (!organization) {
      console.log("❌ No organization found for testing");
      return;
    }

    console.log(`📋 Testing with organization: ${organization.name} (${organization.id})`);

    // Check organization taxes configuration
    if (!organization.taxes) {
      console.log("⚠️  Organization has no tax configuration, creating default...");
      await db.organizationTaxes.create({
        data: {
          organizationId: organization.id,
          pixTransferFixedFee: 10, // 10 cents fee for testing
          pixTransferPercentFee: 0,
          pixChargeFixedFee: 5,
          pixChargePercentFee: 1.5
        }
      });
      console.log("✅ Created default tax configuration");
    } else {
      console.log(`💰 Current tax configuration:`);
      console.log(`   - PIX Transfer Fixed Fee: ${organization.taxes.pixTransferFixedFee} cents`);
      console.log(`   - PIX Transfer Percent Fee: ${organization.taxes.pixTransferPercentFee}%`);
      console.log(`   - PIX Charge Fixed Fee: ${organization.taxes.pixChargeFixedFee} cents`);
      console.log(`   - PIX Charge Percent Fee: ${organization.taxes.pixChargePercentFee}%`);
    }

    // Test fee calculation for different amounts
    const testAmounts = [10, 50, 100, 500, 1000]; // in cents

    console.log("\n🔬 Testing Fee Calculations:");
    console.log("-".repeat(60));

    for (const amount of testAmounts) {
      console.log(`\n💸 Testing transfer of ${amount} cents (R$ ${(amount / 100).toFixed(2)})`);
      
      try {
        const feeCalculation = await calculateTransactionFees(
          organization.id,
          amount,
          'TRANSFER'
        );

        const totalAmount = amount + feeCalculation.totalFee;

        console.log(`   📊 Fee Calculation Result:`);
        console.log(`      - Transfer Amount: ${amount} cents (R$ ${(amount / 100).toFixed(2)})`);
        console.log(`      - Percent Fee: ${feeCalculation.percentFee} cents (R$ ${(feeCalculation.percentFee / 100).toFixed(2)})`);
        console.log(`      - Fixed Fee: ${feeCalculation.fixedFee} cents (R$ ${(feeCalculation.fixedFee / 100).toFixed(2)})`);
        console.log(`      - Total Fee: ${feeCalculation.totalFee} cents (R$ ${(feeCalculation.totalFee / 100).toFixed(2)})`);
        console.log(`      - Total Amount: ${totalAmount} cents (R$ ${(totalAmount / 100).toFixed(2)})`);
        console.log(`      - Fee Source: ${feeCalculation.source}`);

        // Verify the calculation is correct
        const expectedTotalFee = feeCalculation.percentFee + feeCalculation.fixedFee;
        if (Math.abs(feeCalculation.totalFee - expectedTotalFee) > 0.01) {
          console.log(`      ❌ ERROR: Total fee calculation mismatch!`);
          console.log(`         Expected: ${expectedTotalFee}, Got: ${feeCalculation.totalFee}`);
        } else {
          console.log(`      ✅ Fee calculation is correct`);
        }

      } catch (error) {
        console.log(`      ❌ Error calculating fees: ${error instanceof Error ? error.message : error}`);
      }
    }

    // Test with a specific gateway if available
    const activeGateway = organization.gateways.find(og => og.isActive && og.gateway.canSend);
    if (activeGateway) {
      console.log(`\n🏦 Testing with specific gateway: ${activeGateway.gateway.name} (${activeGateway.gateway.type})`);
      
      const testAmount = 100; // 1 real
      const feeCalculation = await calculateTransactionFees(
        organization.id,
        testAmount,
        'TRANSFER',
        activeGateway.gateway.id
      );

      console.log(`   📊 Gateway-specific Fee Calculation:`);
      console.log(`      - Gateway: ${activeGateway.gateway.name}`);
      console.log(`      - Gateway Transfer Fixed Fee: ${activeGateway.gateway.pixTransferFixedFee} cents`);
      console.log(`      - Gateway Transfer Percent Fee: ${activeGateway.gateway.pixTransferPercentFee}%`);
      console.log(`      - Calculated Total Fee: ${feeCalculation.totalFee} cents`);
      console.log(`      - Fee Source: ${feeCalculation.source}`);
    }

    console.log("\n✅ Fee calculation test completed successfully!");

  } catch (error) {
    console.error("❌ Error during fee calculation test:", error);
    process.exit(1);
  }
}

if (require.main === module) {
  main()
    .then(() => {
      console.log("\n🎉 Test completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Test failed:", error);
      process.exit(1);
    });
}
